import axios from 'axios';
import { Box, Typography, Button } from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import RenderApps from './RenderComponents';

const transformDeveloperData = (rawData) => {

  const developerObfuscatedData = {
    developerName: rawData.developer?.info.developerName,
    totalApps: rawData.developer?.info.totalApps,
    firstAppReleasedDate: rawData.developer?.info.firstAppReleasedDate,
    showDeveloperEmail: rawData.developer?.info.developerEmail,
    developerEmail: rawData.developer?.info.developerEmail,
    developerWebsite: rawData.developer?.info.developerWebsite,
    showDeveloperWebsite: rawData.developer?.info.developerWebsite,
    developerAddress: rawData.developer?.info.developerAddress,
    totalInstalls: rawData.developer?.info.totalInstalls,
    totalReviews: rawData.developer?.info.totalReviews,
    totalRatings: rawData.developer?.info.totalRatings,
    latestAppUpdated: rawData.developer?.info.latestAppUpdated,
  }
  const developerApps = rawData.developer.apps?.map(app => ({
    ...app,
    appId: app.appId,
    scoreText: app.scoreText,
    title: app.title,
    appDeveloper: app.appDeveloper,
    showDeveloper: app.appDeveloper,
    genre: app.genre
  }));
  return { developer: { info: developerObfuscatedData, apps: developerApps } };
};
const getDeveloper = async (developerId, countryCode) => {
  try {
    const response = await axios.get(`${process.env.NEXTAUTH_URL}/api/developer-apps?developerID=${developerId}&countryCode=${countryCode}`)

    if (response.status !== 200) {
      console.error("Unexpected status code:", response.status);
      throw new Error("Unexpected status code");
    }

    const formattedDeveloperData = transformDeveloperData(response.data);

    return { data: formattedDeveloperData };
  } catch (error) {
    if (error.response && error.response.status === 404) {
      console.error("Developer information not found");
      return { data: null };
    }
    return { data: null };
  }
};


export default async function DeveloperDetails({ params: { developerId }, searchParams: { countryCode } }) {
  const developerData = await getDeveloper(developerId, countryCode);
  const developer = `Android Apps by ${developerData?.data?.developer?.info?.developerName} on Google Play`;

  return (
    <>
      {developerData.data ? (
        <>
          <RenderApps developerInfo={developerData.data.developer} />
          <metadata>
            <title>{developer}</title>
          </metadata>
        </>
      ) : (
        <>
          <metadata>
            <title>Not Found</title>
          </metadata>
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '80vh',
            textAlign: 'center',
            p: 3
          }}>
            <Box sx={{
              bgcolor: 'background.paper',
              borderRadius: 4,
              p: 4,
              boxShadow: 3,
              maxWidth: '500px',
              width: '100%'
            }}>
              <ErrorOutlineIcon
                sx={{
                  fontSize: 80,
                  color: 'error.main',
                  mb: 2
                }}
              />
              <Typography variant="h5" component="h1" gutterBottom sx={{ fontWeight: 700 }}>
                Developer Not Found
              </Typography>
              <Typography variant="body1" color="text.secondary" paragraph>
                We couldn't find any information for this developer.
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                The developer ID may be incorrect or the account may have been removed.
              </Typography>
              <Button
                variant="contained"
                href="/"
                sx={{
                  backgroundColor: '#00A3FF',
                  '&:hover': { backgroundColor: '#0088cc',color:"white" },
                  mt: 3
                }}
              >
                Back to Home
              </Button>
            </Box>
          </Box>
        </>
      )}
    </>
  );
}