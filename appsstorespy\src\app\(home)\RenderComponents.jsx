"use client";
import React, { useEffect } from "react";
import { BeatLoader } from "react-spinners";
import { connect } from "react-redux";
import { Box } from "@mui/material"
import { useDispatch } from 'react-redux';
import  {selectedUserCountry} from "@/app/redux/slice/topAppSlice";
import TopRanksApps from "../components/topRanksApps/TopRanksApps";
import AppCard from "../components/tableData/appTableView";
import {countries, timezoneToCountryName} from "../utils/countries";


const RenderApps = (props) => {
  const dispatch = useDispatch();
  const {
    searchApps,
    searchParams,
    searchResultsAvailable,
    searchAppsLoading,
    category,
    type
  } = props;


  useEffect(() => {
    if (Intl) {
      let userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      let userCountry = timezoneToCountryName[userTimeZone];
      const cc = countries.find((x) => x.label == userCountry);
      dispatch(selectedUserCountry({
        countryCode: cc?.code || 'US',
        country: userCountry || 'United States'
      }));
    }
  }, [])

  return (
    <>
    {searchAppsLoading ? (
      <Box
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "65vh"
        }}
      >
        <BeatLoader color={"#00A3FF"} loading={searchAppsLoading} size={25} />
      </Box>
    ) : (
      <>
        {searchResultsAvailable ? (
          <AppCard apps={searchApps} searchParams={searchParams}/>
        ) : (
          <TopRanksApps category={category} type={type}/>
        )}
      </>
    )}
  </>
  );
};

const mapStateToProps = ({ topApps }) => ({
  searchApps: topApps?.searchApps,
  searchParams: topApps.searchParams,
  searchResultsAvailable: topApps?.searchResultsAvailable,
  searchAppsLoading: topApps?.searchAppsLoading,
});

export default connect(mapStateToProps)(RenderApps);
