"use client";
import React, { useState } from 'react';
import Link from 'next/link';
import { DOMAINS, SITE_INFO, getCurrentSiteType, isCrossLinkingEnabled } from '../utils/crossLinking';

/**
 * Universal Navigation Component for Cross-Site Linking
 * Provides SEO-friendly navigation between all three sites
 */
const UniversalNavigation = ({ 
  variant = 'header', // 'header', 'footer', 'dropdown'
  className = '',
  showDescriptions = false,
  currentSite = null 
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const currentSiteType = currentSite || getCurrentSiteType();
  
  // Get all sites except current one for navigation
  const otherSites = Object.entries(SITE_INFO).filter(([key]) => {
    if (currentSiteType === 'all-versions') return key !== 'APK_ALL_VERSIONS';
    if (currentSiteType === 'latest-only') return key !== 'APK_LATEST';
    if (currentSiteType === 'ai-tools') return key !== 'AI_TOOLS';
    return key !== 'APP_STORE_SPY';
  });

  // Header variant - horizontal navigation
  if (variant === 'header') {
    return (
      <nav className={`universal-nav-header ${className}`} aria-label="Cross-site navigation">
        <div className="hidden md:flex items-center space-x-6">
          {otherSites.map(([key, site]) => (
            <Link
              key={key}
              href={site.domain}
              className="text-gray-600 hover:text-blue-600 font-medium transition-colors duration-200"
              target="_blank"
              rel="noopener noreferrer"
              title={`Visit ${site.name} - ${site.description}`}
            >
              {site.name}
            </Link>
          ))}
        </div>
        
        {/* Mobile dropdown */}
        <div className="md:hidden relative">
          <button
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className="flex items-center text-gray-600 hover:text-blue-600 font-medium"
            aria-expanded={isDropdownOpen}
            aria-haspopup="true"
          >
            Our Sites
            <svg className="ml-1 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
          
          {isDropdownOpen && (
            <div className="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50">
              <div className="py-1">
                {otherSites.map(([key, site]) => (
                  <Link
                    key={key}
                    href={site.domain}
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={() => setIsDropdownOpen(false)}
                  >
                    <div className="font-medium">{site.name}</div>
                    {showDescriptions && (
                      <div className="text-xs text-gray-500">{site.description}</div>
                    )}
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>
      </nav>
    );
  }

  // Footer variant - vertical list with descriptions
  if (variant === 'footer') {
    return (
      <div className={`universal-nav-footer ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Our Network
        </h3>
        <ul className="space-y-3">
          {otherSites.map(([key, site]) => (
            <li key={key}>
              <Link
                href={site.domain}
                className="group block"
                target="_blank"
                rel="noopener noreferrer"
                title={`Visit ${site.name} - ${site.description}`}
              >
                <div className="font-medium text-gray-600 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  {site.name}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  {site.description}
                </div>
              </Link>
            </li>
          ))}
        </ul>
      </div>
    );
  }

  // Dropdown variant - compact dropdown menu
  if (variant === 'dropdown') {
    return (
      <div className={`universal-nav-dropdown relative ${className}`}>
        <button
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          aria-expanded={isDropdownOpen}
          aria-haspopup="true"
        >
          Explore Our Sites
          <svg className="ml-2 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>

        {isDropdownOpen && (
          <div className="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50">
            <div className="py-2">
              {otherSites.map(([key, site]) => (
                <Link
                  key={key}
                  href={site.domain}
                  className="block px-4 py-3 hover:bg-gray-50 transition-colors"
                  target="_blank"
                  rel="noopener noreferrer"
                  onClick={() => setIsDropdownOpen(false)}
                >
                  <div className="font-medium text-gray-900">{site.name}</div>
                  <div className="text-sm text-gray-500 mt-1">{site.description}</div>
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }

  return null;
};

/**
 * Cross-Site Banner Component
 * Prominent banner for promoting other sites
 */
export const CrossSiteBanner = ({ 
  className = '',
  targetSite = null,
  customMessage = null 
}) => {
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const currentSiteType = getCurrentSiteType();
  let bannerConfig = null;

  // Default banner configurations
  if (currentSiteType === 'ai-tools' && !targetSite) {
    bannerConfig = {
      title: 'Need Mobile Apps & Analytics?',
      description: 'Download Android APKs and analyze app performance',
      sites: [SITE_INFO.APK_ALL_VERSIONS, SITE_INFO.APP_STORE_SPY],
      gradient: 'from-green-50 to-blue-50',
      border: 'border-green-200'
    };
  } else if (currentSiteType === 'all-versions' && !targetSite) {
    bannerConfig = {
      title: 'Discover AI Tools & Analytics',
      description: 'Explore our AI tools platform and app analytics dashboard',
      sites: [SITE_INFO.AI_TOOLS, SITE_INFO.APP_STORE_SPY],
      gradient: 'from-blue-50 to-purple-50',
      border: 'border-blue-200'
    };
  }

  if (!bannerConfig && !targetSite) {
    return null;
  }

  const sites = targetSite ? [SITE_INFO[targetSite]] : bannerConfig.sites;

  return (
    <div className={`bg-gradient-to-r ${bannerConfig?.gradient || 'from-gray-50 to-blue-50'} border ${bannerConfig?.border || 'border-gray-200'} rounded-lg p-6 ${className}`}>
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {customMessage || bannerConfig?.title || 'Explore Our Network'}
        </h3>
        <p className="text-gray-600 mb-4">
          {bannerConfig?.description || 'Discover more tools and resources across our platform'}
        </p>
        <div className="flex flex-wrap justify-center gap-3">
          {sites.map((site, index) => (
            <Link
              key={index}
              href={site.domain}
              className="inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 hover:border-blue-300 transition-all duration-200"
              target="_blank"
              rel="noopener noreferrer"
            >
              Visit {site.name}
              <svg className="ml-2 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

export default UniversalNavigation;
