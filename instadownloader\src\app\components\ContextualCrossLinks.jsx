"use client";
import React from 'react';
import Link from 'next/link';
import { 
  generateLatestAPKURL,
  generateOlderVersionsAPKURL,
  generateAnchorText, 
  trackCrossLinkClick,
  isCrossLinkingEnabled,
  SITE_INFO
} from '../utils/crossLinking';

/**
 * Contextual Cross-Links Component for AI Tools Pages
 * Shows relevant links to other sites based on tool information
 */
const ContextualCrossLinks = ({ 
  toolDetails, 
  variant = 'card', // 'card', 'banner', 'inline'
  className = '',
  maxLinks = 3 
}) => {
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const links = [];

  // Generate APK download links
  const latestAPKURL = generateLatestAPKURL(toolDetails);
  const olderVersionsAPKURL = generateOlderVersionsAPKURL(toolDetails);

  if (latestAPKURL) {
    links.push({
      type: 'apk-latest',
      url: latestAPKURL,
      text: 'Download Latest APK',
      description: `Get the latest Android version of ${toolDetails?.title || 'related apps'}`,
      icon: '📱'
    });
  }

  if (olderVersionsAPKURL) {
    links.push({
      type: 'apk-versions',
      url: olderVersionsAPKURL,
      text: 'All APK Versions',
      description: `Browse all versions of ${toolDetails?.title || 'related apps'}`,
      icon: '📦'
    });
  }

  // Add AppStoreSpy link for app analytics
  if (toolDetails?.category) {
    const categoryMapping = {
      'productivity': 'productivity',
      'business': 'business',
      'education': 'education',
      'content-creation': 'entertainment',
      'image-generation': 'photography',
      'communication': 'communication',
      'social-media': 'social',
      'finance': 'finance',
      'health': 'health',
      'lifestyle': 'lifestyle',
      'e-commerce': 'shopping'
    };
    
    const appCategory = categoryMapping[toolDetails.category.toLowerCase()] || 'productivity';
    links.push({
      type: 'app-analytics',
      url: `${SITE_INFO.APP_STORE_SPY.domain}/top-apps?category=${appCategory}&ref=ai-tools`,
      text: 'View App Analytics',
      description: `Analyze ${appCategory} apps performance and market trends`,
      icon: '📊'
    });
  }

  if (links.length === 0) {
    return null;
  }

  const handleLinkClick = (link) => {
    trackCrossLinkClick('ai-tools', link.type, {
      toolId: toolDetails?.id,
      title: toolDetails?.title,
      category: toolDetails?.category
    });
  };

  // Card variant - prominent display
  if (variant === 'card') {
    return (
      <div className={`bg-gradient-to-br from-green-50 to-blue-50 border border-green-200 rounded-lg p-6 mt-6 ${className}`}>
        <div className="flex items-center mb-4">
          <span className="text-2xl mr-2">🔗</span>
          <h3 className="text-lg font-semibold text-gray-900">
            Related Mobile Resources
          </h3>
        </div>
        
        <p className="text-gray-600 mb-4">
          Discover mobile apps and analytics for {toolDetails?.title || 'this tool'}
        </p>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
          {links.slice(0, maxLinks).map((link, index) => (
            <Link
              key={index}
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="block p-4 bg-white rounded-lg border border-gray-200 hover:border-green-300 hover:shadow-md transition-all duration-200 group"
              onClick={() => handleLinkClick(link)}
            >
              <div className="flex items-center mb-2">
                <span className="text-xl mr-2">{link.icon}</span>
                <span className="font-medium text-gray-900 group-hover:text-green-600">
                  {link.text}
                </span>
              </div>
              <p className="text-sm text-gray-500">
                {link.description}
              </p>
            </Link>
          ))}
        </div>
      </div>
    );
  }

  // Banner variant - horizontal layout
  if (variant === 'banner') {
    return (
      <div className={`bg-gradient-to-r from-green-600 to-blue-600 rounded-lg p-6 mt-6 text-white ${className}`}>
        <div className="flex items-center justify-between flex-wrap gap-4">
          <div>
            <h3 className="text-lg font-semibold mb-2">
              📱 Get Mobile Apps
            </h3>
            <p className="opacity-90">
              Download Android APKs or analyze app performance data
            </p>
          </div>
          
          <div className="flex gap-3 flex-wrap">
            {links.slice(0, 2).map((link, index) => (
              <Link
                key={index}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 rounded-md hover:bg-opacity-30 transition-all duration-200 font-medium"
                onClick={() => handleLinkClick(link)}
              >
                <span className="mr-2">{link.icon}</span>
                {link.text}
              </Link>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Inline variant - compact chips
  if (variant === 'inline') {
    return (
      <div className={`mt-4 ${className}`}>
        <p className="text-sm text-gray-600 mb-2">Related mobile resources:</p>
        <div className="flex gap-2 flex-wrap">
          {links.slice(0, maxLinks).map((link, index) => (
            <Link
              key={index}
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm hover:bg-green-200 transition-colors duration-200"
              onClick={() => handleLinkClick(link)}
            >
              <span className="mr-1">{link.icon}</span>
              {link.text}
            </Link>
          ))}
        </div>
      </div>
    );
  }

  return null;
};

/**
 * Tool Overview Cross-Links Component
 * Specifically designed for the overview section of tool details
 */
export const ToolOverviewCrossLinks = ({ toolDetails, className = '' }) => {
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const links = [];

  // Generate APK download links
  const latestAPKURL = generateLatestAPKURL(toolDetails);
  const olderVersionsAPKURL = generateOlderVersionsAPKURL(toolDetails);

  if (latestAPKURL) {
    links.push({
      type: 'apk-latest',
      url: latestAPKURL,
      text: 'Mobile Apps',
      description: 'Download related Android apps',
      icon: '📱'
    });
  }

  // Add AppStoreSpy link
  if (toolDetails?.category) {
    links.push({
      type: 'app-analytics',
      url: `${SITE_INFO.APP_STORE_SPY.domain}/top-apps?ref=ai-tools`,
      text: 'App Analytics',
      description: 'View mobile app performance',
      icon: '📊'
    });
  }

  if (links.length === 0) {
    return null;
  }

  return (
    <div className={`mt-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Related Mobile Resources
      </h3>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {links.map((link, index) => (
          <Link
            key={index}
            href={link.url}
            target="_blank"
            rel="noopener noreferrer"
            className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-green-300 hover:shadow-lg transition-all duration-200 group"
            onClick={() => trackCrossLinkClick('ai-tools', link.type, {
              toolId: toolDetails?.id,
              title: toolDetails?.title,
              category: toolDetails?.category
            })}
          >
            <div className="flex items-center mb-2">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3 group-hover:bg-green-200 transition-colors">
                <span className="text-lg">{link.icon}</span>
              </div>
              <span className="font-medium text-gray-900 group-hover:text-green-600">
                {link.text}
              </span>
            </div>
            <p className="text-sm text-gray-500">
              {link.description}
            </p>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default ContextualCrossLinks;
