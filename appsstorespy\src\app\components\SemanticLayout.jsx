"use client";
import React from 'react';
import { Box } from '@mui/material';

/**
 * Semantic HTML Layout Component
 * Provides proper semantic structure for better SEO and accessibility
 */
const SemanticLayout = ({ 
  children,
  headerContent,
  navContent,
  asideContent,
  footerContent,
  className = '',
  mainClassName = '',
  role = null
}) => {
  return (
    <Box 
      component="div" 
      className={`semantic-layout ${className}`}
      sx={{ 
        display: 'flex', 
        flexDirection: 'column', 
        minHeight: '100vh' 
      }}
    >
      {/* Header Section */}
      {headerContent && (
        <Box 
          component="header" 
          role="banner"
          sx={{ 
            position: 'sticky', 
            top: 0, 
            zIndex: 1100,
            backgroundColor: 'background.paper',
            borderBottom: '1px solid',
            borderColor: 'divider'
          }}
        >
          {headerContent}
        </Box>
      )}

      {/* Navigation Section */}
      {navContent && (
        <Box 
          component="nav" 
          role="navigation"
          aria-label="Main navigation"
          sx={{ 
            backgroundColor: 'background.default',
            borderBottom: '1px solid',
            borderColor: 'divider'
          }}
        >
          {navContent}
        </Box>
      )}

      {/* Main Content Area */}
      <Box 
        sx={{ 
          display: 'flex', 
          flex: 1,
          flexDirection: { xs: 'column', md: 'row' }
        }}
      >
        {/* Sidebar/Aside Content */}
        {asideContent && (
          <Box 
            component="aside" 
            role="complementary"
            aria-label="Sidebar content"
            sx={{ 
              width: { xs: '100%', md: '300px' },
              backgroundColor: 'background.paper',
              borderRight: { md: '1px solid' },
              borderColor: 'divider',
              p: 2
            }}
          >
            {asideContent}
          </Box>
        )}

        {/* Main Content */}
        <Box 
          component="main" 
          role={role || "main"}
          aria-label="Main content"
          className={mainClassName}
          sx={{ 
            flex: 1,
            p: { xs: 2, md: 3 },
            backgroundColor: 'background.default'
          }}
        >
          {children}
        </Box>
      </Box>

      {/* Footer Section */}
      {footerContent && (
        <Box 
          component="footer" 
          role="contentinfo"
          sx={{ 
            mt: 'auto',
            backgroundColor: 'background.paper',
            borderTop: '1px solid',
            borderColor: 'divider'
          }}
        >
          {footerContent}
        </Box>
      )}
    </Box>
  );
};

/**
 * Article Layout Component
 * Semantic structure for article/blog content
 */
export const ArticleLayout = ({ 
  title,
  subtitle,
  author,
  publishDate,
  lastModified,
  content,
  relatedContent,
  breadcrumbs,
  tags = [],
  className = ''
}) => {
  return (
    <article 
      className={`article-layout ${className}`}
      itemScope 
      itemType="https://schema.org/Article"
    >
      {/* Breadcrumbs */}
      {breadcrumbs && (
        <nav aria-label="Breadcrumb" className="mb-4">
          <ol 
            className="flex items-center space-x-2 text-sm text-gray-600"
            itemScope 
            itemType="https://schema.org/BreadcrumbList"
          >
            {breadcrumbs.map((crumb, index) => (
              <li 
                key={index}
                itemProp="itemListElement" 
                itemScope 
                itemType="https://schema.org/ListItem"
                className="flex items-center"
              >
                {index > 0 && <span className="mx-2">/</span>}
                {crumb.href ? (
                  <a 
                    href={crumb.href}
                    itemProp="item"
                    className="hover:text-blue-600"
                  >
                    <span itemProp="name">{crumb.name}</span>
                  </a>
                ) : (
                  <span itemProp="name" className="text-gray-900">{crumb.name}</span>
                )}
                <meta itemProp="position" content={index + 1} />
              </li>
            ))}
          </ol>
        </nav>
      )}

      {/* Article Header */}
      <header className="mb-6">
        <h1 
          className="text-3xl font-bold text-gray-900 mb-2"
          itemProp="headline"
        >
          {title}
        </h1>
        
        {subtitle && (
          <h2 
            className="text-xl text-gray-600 mb-4"
            itemProp="alternativeHeadline"
          >
            {subtitle}
          </h2>
        )}

        {/* Article Meta */}
        <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
          {author && (
            <div 
              itemProp="author" 
              itemScope 
              itemType="https://schema.org/Person"
            >
              By <span itemProp="name">{author}</span>
            </div>
          )}
          
          {publishDate && (
            <time 
              dateTime={publishDate}
              itemProp="datePublished"
            >
              Published: {new Date(publishDate).toLocaleDateString()}
            </time>
          )}
          
          {lastModified && (
            <time 
              dateTime={lastModified}
              itemProp="dateModified"
            >
              Updated: {new Date(lastModified).toLocaleDateString()}
            </time>
          )}
        </div>

        {/* Tags */}
        {tags.length > 0 && (
          <div className="mt-4">
            <span className="text-sm text-gray-500 mr-2">Tags:</span>
            {tags.map((tag, index) => (
              <span 
                key={index}
                className="inline-block bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs mr-2 mb-1"
                itemProp="keywords"
              >
                {tag}
              </span>
            ))}
          </div>
        )}
      </header>

      {/* Article Content */}
      <section 
        className="prose max-w-none mb-8"
        itemProp="articleBody"
      >
        {content}
      </section>

      {/* Related Content */}
      {relatedContent && (
        <aside 
          className="mt-8 p-4 bg-gray-50 rounded-lg"
          aria-label="Related content"
        >
          <h3 className="text-lg font-semibold mb-4">Related Content</h3>
          {relatedContent}
        </aside>
      )}
    </article>
  );
};

/**
 * App Details Layout Component
 * Semantic structure for app/tool detail pages
 */
export const AppDetailsLayout = ({ 
  appInfo,
  screenshots,
  description,
  specifications,
  reviews,
  relatedApps,
  downloadSection,
  className = ''
}) => {
  return (
    <article 
      className={`app-details-layout ${className}`}
      itemScope 
      itemType="https://schema.org/SoftwareApplication"
    >
      {/* App Header */}
      <header className="mb-6">
        <div className="flex items-start gap-4">
          {appInfo.icon && (
            <img 
              src={appInfo.icon}
              alt={`${appInfo.title} icon`}
              className="w-16 h-16 rounded-lg"
              itemProp="image"
            />
          )}
          <div className="flex-1">
            <h1 
              className="text-2xl font-bold text-gray-900"
              itemProp="name"
            >
              {appInfo.title}
            </h1>
            {appInfo.developer && (
              <div 
                className="text-gray-600"
                itemProp="author"
                itemScope
                itemType="https://schema.org/Organization"
              >
                by <span itemProp="name">{appInfo.developer}</span>
              </div>
            )}
            {appInfo.category && (
              <span 
                className="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm mt-2"
                itemProp="applicationCategory"
              >
                {appInfo.category}
              </span>
            )}
          </div>
        </div>
      </header>

      {/* Screenshots Section */}
      {screenshots && (
        <section aria-label="Screenshots" className="mb-6">
          <h2 className="text-lg font-semibold mb-3">Screenshots</h2>
          {screenshots}
        </section>
      )}

      {/* Description Section */}
      {description && (
        <section aria-label="Description" className="mb-6">
          <h2 className="text-lg font-semibold mb-3">Description</h2>
          <div itemProp="description">
            {description}
          </div>
        </section>
      )}

      {/* Download Section */}
      {downloadSection && (
        <section aria-label="Download" className="mb-6">
          <h2 className="text-lg font-semibold mb-3">Download</h2>
          {downloadSection}
        </section>
      )}

      {/* Specifications Section */}
      {specifications && (
        <section aria-label="Specifications" className="mb-6">
          <h2 className="text-lg font-semibold mb-3">Specifications</h2>
          {specifications}
        </section>
      )}

      {/* Reviews Section */}
      {reviews && (
        <section aria-label="Reviews" className="mb-6">
          <h2 className="text-lg font-semibold mb-3">Reviews</h2>
          {reviews}
        </section>
      )}

      {/* Related Apps */}
      {relatedApps && (
        <aside aria-label="Related apps" className="mt-8">
          <h2 className="text-lg font-semibold mb-3">Related Apps</h2>
          {relatedApps}
        </aside>
      )}
    </article>
  );
};

export default SemanticLayout;
