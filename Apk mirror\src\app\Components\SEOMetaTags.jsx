import Head from 'next/head';
import { generateAppStructuredData } from '../utils/seoOptimization';

/**
 * Comprehensive SEO Meta Tags Component for APK Mirror
 * Includes Open Graph, Twitter Cards, and structured data
 */
const SEOMetaTags = ({
  title,
  description,
  keywords = [],
  image,
  url,
  type = 'website',
  appDetails = null,
  relatedAITools = [],
  author = 'APKExplorer',
  siteName = 'APKExplorer',
  locale = 'en_US',
  publishedTime = null,
  modifiedTime = null
}) => {
  // Generate structured data if app details are provided
  const structuredData = appDetails ? 
    generateAppStructuredData(appDetails, relatedAITools) : null;

  // Default values
  const defaultTitle = 'APKExplorer - Fast Android APK Downloader';
  const defaultDescription = 'APKExplorer is your go-to source for downloading Android APKs quickly and securely. Discover a vast library of apps, explore different versions, and stay updated with the latest releases.';
  const defaultImage = '/images/apkexplorer-og.jpg';
  const defaultUrl = 'https://apkdemo1.com';

  const finalTitle = title || defaultTitle;
  const finalDescription = description || defaultDescription;
  const finalImage = image || defaultImage;
  const finalUrl = url || defaultUrl;

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{finalTitle}</title>
      <meta name="description" content={finalDescription} />
      {keywords.length > 0 && (
        <meta name="keywords" content={keywords.join(', ')} />
      )}
      <meta name="author" content={author} />
      <meta name="robots" content="index, follow" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="language" content="English" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={finalUrl} />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={finalTitle} />
      <meta property="og:description" content={finalDescription} />
      <meta property="og:image" content={finalImage} />
      <meta property="og:url" content={finalUrl} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content={locale} />
      {publishedTime && (
        <meta property="article:published_time" content={publishedTime} />
      )}
      {modifiedTime && (
        <meta property="article:modified_time" content={modifiedTime} />
      )}
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={finalTitle} />
      <meta name="twitter:description" content={finalDescription} />
      <meta name="twitter:image" content={finalImage} />
      <meta name="twitter:url" content={finalUrl} />
      <meta name="twitter:site" content="@apkexplorer" />
      <meta name="twitter:creator" content="@apkexplorer" />
      
      {/* Additional Meta Tags for Better SEO */}
      <meta name="theme-color" content="#3B82F6" />
      <meta name="msapplication-TileColor" content="#3B82F6" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content={siteName} />
      
      {/* Favicon and Icons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />
      
      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData)
          }}
        />
      )}
      
      {/* Organization Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": siteName,
            "url": defaultUrl,
            "logo": `${defaultUrl}/logo.png`,
            "description": defaultDescription,
            "sameAs": [
              "https://twitter.com/apkexplorer"
            ]
          })
        }}
      />
      
      {/* Website Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebSite",
            "name": siteName,
            "url": defaultUrl,
            "description": defaultDescription,
            "potentialAction": {
              "@type": "SearchAction",
              "target": {
                "@type": "EntryPoint",
                "urlTemplate": `${defaultUrl}/app-search?q={search_term_string}`
              },
              "query-input": "required name=search_term_string"
            }
          })
        }}
      />
    </Head>
  );
};

/**
 * App-specific SEO Meta Tags for APK pages
 */
export const AppSEOMetaTags = ({ appDetails, relatedAITools = [] }) => {
  if (!appDetails) return null;

  const title = `${appDetails.title} APK Download - Free Android App | APKExplorer`;
  const description = `Download ${appDetails.title} APK for Android. ${appDetails.description ? appDetails.description.substring(0, 120) + '...' : 'Free, safe, and secure APK download.'} Get all versions and updates.`;
  const keywords = [
    `${appDetails.title} APK`,
    'Android APK download',
    'free APK',
    appDetails.category,
    'mobile app',
    'Android application',
    `${appDetails.title} download`,
    'APK file'
  ];
  const url = `https://apkdemo1.com/apps/appdetails/${appDetails.appId}`;
  const image = appDetails.icon || '/images/apk-download-og.jpg';

  return (
    <SEOMetaTags
      title={title}
      description={description}
      keywords={keywords}
      image={image}
      url={url}
      type="article"
      appDetails={appDetails}
      relatedAITools={relatedAITools}
      publishedTime={appDetails.publishedDate}
      modifiedTime={appDetails.lastUpdated}
    />
  );
};

/**
 * Category-specific SEO Meta Tags for APK categories
 */
export const CategorySEOMetaTags = ({ category, apps = [] }) => {
  const title = `${category} APK Downloads - Free Android Apps | APKExplorer`;
  const description = `Download the best ${category} APK files for Android. Free, safe, and secure downloads of ${category} applications. Get the latest versions and updates.`;
  const keywords = [
    `${category} APK`,
    'Android apps',
    'free APK download',
    `${category} applications`,
    'mobile apps',
    'Android software',
    category.toLowerCase()
  ];
  const url = `https://apkdemo1.com/${category.toLowerCase()}`;

  return (
    <SEOMetaTags
      title={title}
      description={description}
      keywords={keywords}
      url={url}
      type="website"
    />
  );
};

export default SEOMetaTags;
