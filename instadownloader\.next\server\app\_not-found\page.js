/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring2%5Cinstadownloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring2%5Cinstadownloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring2%5Cinstadownloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring2%5Cinstadownloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring2%5Cinstadownloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring2%5Cinstadownloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistMonoVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5CAds.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5Cnavbar%5C%5CNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5CUniversalNavigation.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistMonoVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5CAds.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5Cnavbar%5C%5CNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5CUniversalNavigation.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/Ads.jsx */ \"(ssr)/./src/app/Ads.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/components/navbar/Navbar.jsx */ \"(ssr)/./src/app/components/navbar/Navbar.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/components/UniversalNavigation.jsx */ \"(ssr)/./src/app/components/UniversalNavigation.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistMonoVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5CAds.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5Cnavbar%5C%5CNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5CUniversalNavigation.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring2%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/Ads.jsx":
/*!*************************!*\
  !*** ./src/app/Ads.jsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AdComponent({ adSlot, style = {\n    display: \"block\"\n} }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        try {\n            (window.adsbygoogle = window.adsbygoogle || []).push({});\n        } catch (err) {\n            console.error(\"AdSense error:\", err);\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ins\", {\n        className: \"adsbygoogle\",\n        style: style,\n        \"data-ad-client\": \"ca-pub-2219975169694529\",\n        \"data-ad-slot\": adSlot,\n        \"data-ad-format\": \"auto\",\n        \"data-full-width-responsive\": \"true\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\Ads.jsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL0Fkcy5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ2tDO0FBRW5CLFNBQVNDLFlBQVksRUFBRUMsTUFBTSxFQUFFQyxRQUFRO0lBQUVDLFNBQVM7QUFBUSxDQUFDLEVBQUU7SUFDMUVKLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSTtZQUNESyxDQUFBQSxPQUFPQyxXQUFXLEdBQUdELE9BQU9DLFdBQVcsSUFBSSxFQUFFLEVBQUVDLElBQUksQ0FBQyxDQUFDO1FBQ3hELEVBQUUsT0FBT0MsS0FBSztZQUNaQyxRQUFRQyxLQUFLLENBQUMsa0JBQWtCRjtRQUNsQztJQUNGLEdBQUcsRUFBRTtJQUVMLHFCQUNFLDhEQUFDRztRQUNDQyxXQUFVO1FBQ1ZULE9BQU9BO1FBQ1BVLGtCQUFlO1FBQ2ZDLGdCQUFjWjtRQUNkYSxrQkFBZTtRQUNmQyw4QkFBMkI7Ozs7OztBQUdqQyIsInNvdXJjZXMiOlsid2VicGFjazovL3Rvb2xwbGF0ZS5haS8uL3NyYy9hcHAvQWRzLmpzeD82NzNiIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcbmltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBZENvbXBvbmVudCh7IGFkU2xvdCwgc3R5bGUgPSB7IGRpc3BsYXk6IFwiYmxvY2tcIiB9IH0pIHtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICB0cnkge1xuICAgICAgKHdpbmRvdy5hZHNieWdvb2dsZSA9IHdpbmRvdy5hZHNieWdvb2dsZSB8fCBbXSkucHVzaCh7fSk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiQWRTZW5zZSBlcnJvcjpcIiwgZXJyKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICByZXR1cm4gKFxuICAgIDxpbnNcbiAgICAgIGNsYXNzTmFtZT1cImFkc2J5Z29vZ2xlXCJcbiAgICAgIHN0eWxlPXtzdHlsZX1cbiAgICAgIGRhdGEtYWQtY2xpZW50PVwiY2EtcHViLTIyMTk5NzUxNjk2OTQ1MjlcIlxuICAgICAgZGF0YS1hZC1zbG90PXthZFNsb3R9XG4gICAgICBkYXRhLWFkLWZvcm1hdD1cImF1dG9cIlxuICAgICAgZGF0YS1mdWxsLXdpZHRoLXJlc3BvbnNpdmU9XCJ0cnVlXCJcbiAgICA+PC9pbnM+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwiQWRDb21wb25lbnQiLCJhZFNsb3QiLCJzdHlsZSIsImRpc3BsYXkiLCJ3aW5kb3ciLCJhZHNieWdvb2dsZSIsInB1c2giLCJlcnIiLCJjb25zb2xlIiwiZXJyb3IiLCJpbnMiLCJjbGFzc05hbWUiLCJkYXRhLWFkLWNsaWVudCIsImRhdGEtYWQtc2xvdCIsImRhdGEtYWQtZm9ybWF0IiwiZGF0YS1mdWxsLXdpZHRoLXJlc3BvbnNpdmUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/Ads.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/components/UniversalNavigation.jsx":
/*!****************************************************!*\
  !*** ./src/app/components/UniversalNavigation.jsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CrossSiteBanner: () => (/* binding */ CrossSiteBanner),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _utils_crossLinking__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/crossLinking */ \"(ssr)/./src/app/utils/crossLinking.js\");\n/* __next_internal_client_entry_do_not_use__ CrossSiteBanner,default auto */ \n\n\n\n/**\n * Universal Navigation Component for Cross-Site Linking\n * Provides SEO-friendly navigation between all three sites\n */ const UniversalNavigation = ({ variant = \"header\", className = \"\", showDescriptions = false, currentSite = null })=>{\n    const [isDropdownOpen, setIsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (!(0,_utils_crossLinking__WEBPACK_IMPORTED_MODULE_3__.isCrossLinkingEnabled)()) {\n        return null;\n    }\n    const currentSiteType = currentSite || (0,_utils_crossLinking__WEBPACK_IMPORTED_MODULE_3__.getCurrentSiteType)();\n    // Get all sites except current one for navigation\n    const otherSites = Object.entries(_utils_crossLinking__WEBPACK_IMPORTED_MODULE_3__.SITE_INFO).filter(([key])=>{\n        if (currentSiteType === \"all-versions\") return key !== \"APK_ALL_VERSIONS\";\n        if (currentSiteType === \"latest-only\") return key !== \"APK_LATEST\";\n        if (currentSiteType === \"ai-tools\") return key !== \"AI_TOOLS\";\n        return key !== \"APP_STORE_SPY\";\n    });\n    // Header variant - horizontal navigation\n    if (variant === \"header\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: `universal-nav-header ${className}`,\n            \"aria-label\": \"Cross-site navigation\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:flex items-center space-x-6\",\n                    children: otherSites.map(([key, site])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: site.domain,\n                            className: \"text-gray-600 hover:text-blue-600 font-medium transition-colors duration-200\",\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            title: `Visit ${site.name} - ${site.description}`,\n                            children: site.name\n                        }, key, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsDropdownOpen(!isDropdownOpen),\n                            className: \"flex items-center text-gray-600 hover:text-blue-600 font-medium\",\n                            \"aria-expanded\": isDropdownOpen,\n                            \"aria-haspopup\": \"true\",\n                            children: [\n                                \"Our Sites\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"ml-1 h-4 w-4\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, undefined),\n                        isDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-1\",\n                                children: otherSites.map(([key, site])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: site.domain,\n                                        className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        onClick: ()=>setIsDropdownOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: site.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            showDescriptions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: site.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, key, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                                lineNumber: 67,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Footer variant - vertical list with descriptions\n    if (variant === \"footer\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `universal-nav-footer ${className}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4\",\n                    children: \"Our Network\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"space-y-3\",\n                    children: otherSites.map(([key, site])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: site.domain,\n                                className: \"group block\",\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                title: `Visit ${site.name} - ${site.description}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-gray-600 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\",\n                                        children: site.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400 mt-1\",\n                                        children: site.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                                lineNumber: 101,\n                                columnNumber: 15\n                            }, undefined)\n                        }, key, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                            lineNumber: 100,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Dropdown variant - compact dropdown menu\n    if (variant === \"dropdown\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `universal-nav-dropdown relative ${className}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setIsDropdownOpen(!isDropdownOpen),\n                    className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                    \"aria-expanded\": isDropdownOpen,\n                    \"aria-haspopup\": \"true\",\n                    children: [\n                        \"Explore Our Sites\",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"ml-2 h-4 w-4\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 20 20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                d: \"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\",\n                                clipRule: \"evenodd\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, undefined),\n                isDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-2\",\n                        children: otherSites.map(([key, site])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: site.domain,\n                                className: \"block px-4 py-3 hover:bg-gray-50 transition-colors\",\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                onClick: ()=>setIsDropdownOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-gray-900\",\n                                        children: site.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500 mt-1\",\n                                        children: site.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, key, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                                lineNumber: 142,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                        lineNumber: 140,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                    lineNumber: 139,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, undefined);\n    }\n    return null;\n};\n/**\n * Cross-Site Banner Component\n * Prominent banner for promoting other sites\n */ const CrossSiteBanner = ({ className = \"\", targetSite = null, customMessage = null })=>{\n    if (!(0,_utils_crossLinking__WEBPACK_IMPORTED_MODULE_3__.isCrossLinkingEnabled)()) {\n        return null;\n    }\n    const currentSiteType = (0,_utils_crossLinking__WEBPACK_IMPORTED_MODULE_3__.getCurrentSiteType)();\n    let bannerConfig = null;\n    // Default banner configurations\n    if (currentSiteType === \"ai-tools\" && !targetSite) {\n        bannerConfig = {\n            title: \"Need Mobile Apps & Analytics?\",\n            description: \"Download Android APKs and analyze app performance\",\n            sites: [\n                _utils_crossLinking__WEBPACK_IMPORTED_MODULE_3__.SITE_INFO.APK_ALL_VERSIONS,\n                _utils_crossLinking__WEBPACK_IMPORTED_MODULE_3__.SITE_INFO.APP_STORE_SPY\n            ],\n            gradient: \"from-green-50 to-blue-50\",\n            border: \"border-green-200\"\n        };\n    } else if (currentSiteType === \"all-versions\" && !targetSite) {\n        bannerConfig = {\n            title: \"Discover AI Tools & Analytics\",\n            description: \"Explore our AI tools platform and app analytics dashboard\",\n            sites: [\n                _utils_crossLinking__WEBPACK_IMPORTED_MODULE_3__.SITE_INFO.AI_TOOLS,\n                _utils_crossLinking__WEBPACK_IMPORTED_MODULE_3__.SITE_INFO.APP_STORE_SPY\n            ],\n            gradient: \"from-blue-50 to-purple-50\",\n            border: \"border-blue-200\"\n        };\n    }\n    if (!bannerConfig && !targetSite) {\n        return null;\n    }\n    const sites = targetSite ? [\n        _utils_crossLinking__WEBPACK_IMPORTED_MODULE_3__.SITE_INFO[targetSite]\n    ] : bannerConfig.sites;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `bg-gradient-to-r ${bannerConfig?.gradient || \"from-gray-50 to-blue-50\"} border ${bannerConfig?.border || \"border-gray-200\"} rounded-lg p-6 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                    children: customMessage || bannerConfig?.title || \"Explore Our Network\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-4\",\n                    children: bannerConfig?.description || \"Discover more tools and resources across our platform\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap justify-center gap-3\",\n                    children: sites.map((site, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: site.domain,\n                            className: \"inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 hover:border-blue-300 transition-all duration-200\",\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            children: [\n                                \"Visit \",\n                                site.name,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"ml-2 h-4 w-4\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n            lineNumber: 207,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\UniversalNavigation.jsx\",\n        lineNumber: 206,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UniversalNavigation);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/UniversalNavigation.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/components/navbar/Navbar.jsx":
/*!**********************************************!*\
  !*** ./src/app/components/navbar/Navbar.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _UniversalNavigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../UniversalNavigation */ \"(ssr)/./src/app/components/UniversalNavigation.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst Navbar = ()=>{\n    const pathName = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 left-0 right-0 z-10 bg-white shadow\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"bg-white border-gray-200 dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: \"/\",\n                        className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            src: \"/st_logo.png\",\n                            alt: \"ToolifyAI Logo\",\n                            width: 100,\n                            height: 40,\n                            className: \"h-auto w-auto\",\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                            lineNumber: 24,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UniversalNavigation__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        variant: \"header\",\n                        className: \"flex-1 justify-center\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex md:order-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            \"aria-controls\": \"navbar-search\",\n                            \"aria-expanded\": isMenuOpen,\n                            className: \"grid place-items-center md:hidden text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5\",\n                            onClick: toggleMenu,\n                            children: [\n                                isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    \"aria-hidden\": \"true\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        stroke: \"currentColor\",\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    \"aria-hidden\": \"true\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        stroke: \"currentColor\",\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M3 6h14M3 10h14m-7 4h7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Toggle navigation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                            lineNumber: 41,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `w-full md:flex ml-auto md:w-auto md:order-1 ${isMenuOpen ? \"block\" : \"hidden\"}`,\n                        id: \"navbar-search\",\n                        onClick: toggleMenu,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"flex flex-col p-4 md:p-0 mt-4 font-medium border text-transform: uppercase border-gray-100 rounded-lg bg-gray-50 md:space-x-6 lg:space-x-8 rtl:space-x-reverse md:flex-row md:mt-0 md:border-0 md:bg-white dark:bg-gray-800 md:dark:bg-gray-900 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/\",\n                                        className: `block py-2 px-3 text-gray-900 rounded hover:bg-blue-700 hover:text-white md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-blue-500 dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700\r\n                    ${pathName === \"/\" && \"md:text-blue-700 decoration-blue-700\"}\r\n                 `,\n                                        \"aria-current\": \"page\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/tool\",\n                                        className: `block py-2 px-3 text-gray-900 rounded hover:bg-blue-700 hover:text-white md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-blue-500 dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700 ${(pathName === \"/tool\" || pathName === \"/tools\") && \"md:text-blue-700 decoration-blue-700\"}`,\n                                        children: \"Tools\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/tool-search\",\n                                        className: `block py-2 px-3 text-gray-900 rounded hover:bg-blue-700 hover:text-white md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-blue-500 dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700 ${pathName === \"/tool-search\" && \"md:text-blue-700 decoration-blue-700\"}`,\n                                        children: \"Search tools\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navbar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/navbar/Navbar.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/utils/crossLinking.js":
/*!***************************************!*\
  !*** ./src/app/utils/crossLinking.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AI_TO_APK_MAPPING: () => (/* binding */ AI_TO_APK_MAPPING),\n/* harmony export */   DOMAINS: () => (/* binding */ DOMAINS),\n/* harmony export */   SITE_INFO: () => (/* binding */ SITE_INFO),\n/* harmony export */   generateAIToolsURL: () => (/* binding */ generateAIToolsURL),\n/* harmony export */   generateAPKURL: () => (/* binding */ generateAPKURL),\n/* harmony export */   generateAnchorText: () => (/* binding */ generateAnchorText),\n/* harmony export */   generateLatestAPKURL: () => (/* binding */ generateLatestAPKURL),\n/* harmony export */   generateOlderVersionsAPKURL: () => (/* binding */ generateOlderVersionsAPKURL),\n/* harmony export */   getCurrentSiteType: () => (/* binding */ getCurrentSiteType),\n/* harmony export */   getRelevantAPKCategories: () => (/* binding */ getRelevantAPKCategories),\n/* harmony export */   isCrossLinkingEnabled: () => (/* binding */ isCrossLinkingEnabled),\n/* harmony export */   trackCrossLinkClick: () => (/* binding */ trackCrossLinkClick)\n/* harmony export */ });\n/* harmony import */ var _toolMappingService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./toolMappingService */ \"(ssr)/./src/app/utils/toolMappingService.js\");\n/**\n * Cross-linking utilities for SEO-friendly navigation between APK and AI Tools sites\n */ \n// Three-domain configuration\nconst DOMAINS = {\n    APK_ALL_VERSIONS: \"https://apk-mirror.com\" || 0,\n    APK_LATEST: \"https://appsstorespy.io\" || 0,\n    AI_TOOLS: \"https://instadownloader.app\" || 0,\n    APP_STORE_SPY: process.env.NEXT_PUBLIC_APP_STORE_SPY_DOMAIN || \"https://appsstorespy.com\"\n};\n// Site information for navigation\nconst SITE_INFO = {\n    APK_ALL_VERSIONS: {\n        name: \"APKExplorer\",\n        description: \"Download Android APKs - All Versions\",\n        domain: DOMAINS.APK_ALL_VERSIONS\n    },\n    APK_LATEST: {\n        name: \"APKExplorer Latest\",\n        description: \"Download Latest Android APKs\",\n        domain: DOMAINS.APK_LATEST\n    },\n    AI_TOOLS: {\n        name: \"SmartTools\",\n        description: \"Discover Top AI Tools & Reviews\",\n        domain: DOMAINS.AI_TOOLS\n    },\n    APP_STORE_SPY: {\n        name: \"AppsStoreSpy\",\n        description: \"App Analytics & Market Intelligence\",\n        domain: DOMAINS.APP_STORE_SPY\n    }\n};\n// Get current site type\nconst getCurrentSiteType = ()=>{\n    return \"ai-tools\" || 0;\n};\n// Check if cross-linking is enabled\nconst isCrossLinkingEnabled = ()=>{\n    return \"true\" === \"true\";\n};\n/**\n * Category mapping between AI tool categories and APK categories\n */ const AI_TO_APK_MAPPING = {\n    // AI tool categories to APK categories\n    \"productivity\": [\n        \"productivity\",\n        \"business\",\n        \"tools\"\n    ],\n    \"social-media\": [\n        \"communication\",\n        \"social\",\n        \"dating\"\n    ],\n    \"email-generator\": [\n        \"communication\",\n        \"business\"\n    ],\n    \"writing-generator\": [\n        \"productivity\",\n        \"education\",\n        \"news-magazines\"\n    ],\n    \"blog-generator\": [\n        \"news-magazines\",\n        \"productivity\"\n    ],\n    \"business\": [\n        \"business\",\n        \"productivity\",\n        \"finance\"\n    ],\n    \"automation\": [\n        \"tools\",\n        \"productivity\"\n    ],\n    \"art\": [\n        \"art-design\",\n        \"photography\"\n    ],\n    \"graphic-design\": [\n        \"art-design\",\n        \"photography\"\n    ],\n    \"image-generation\": [\n        \"art-design\",\n        \"photography\"\n    ],\n    \"video-generation\": [\n        \"video-players\",\n        \"entertainment\"\n    ],\n    \"music\": [\n        \"music-audio\",\n        \"entertainment\"\n    ],\n    \"education\": [\n        \"education\",\n        \"books-reference\"\n    ],\n    \"research\": [\n        \"education\",\n        \"books-reference\"\n    ],\n    \"health\": [\n        \"health-fitness\",\n        \"medical\"\n    ],\n    \"lifestyle\": [\n        \"lifestyle\",\n        \"health-fitness\"\n    ],\n    \"finace\": [\n        \"finance\",\n        \"business\"\n    ],\n    \"startup\": [\n        \"business\",\n        \"productivity\"\n    ],\n    \"marketing\": [\n        \"business\",\n        \"social\"\n    ],\n    \"developer-tools\": [\n        \"tools\",\n        \"libraries-demo\"\n    ],\n    \"communication\": [\n        \"communication\",\n        \"social\"\n    ],\n    \"translation\": [\n        \"education\",\n        \"tools\"\n    ],\n    \"avatar\": [\n        \"personalization\",\n        \"art-design\"\n    ],\n    \"anime\": [\n        \"entertainment\",\n        \"art-design\"\n    ]\n};\n/**\n * Get relevant APK categories for an AI tool category\n */ const getRelevantAPKCategories = (aiCategory)=>{\n    if (!aiCategory) return [\n        \"productivity\"\n    ]; // Default fallback\n    const normalizedCategory = aiCategory.toLowerCase().replace(/[^a-z0-9]/g, \"-\");\n    return AI_TO_APK_MAPPING[normalizedCategory] || [\n        \"productivity\"\n    ];\n};\n/**\n * Generate latest APK download URL for an AI tool\n */ const generateLatestAPKURL = (toolDetails)=>{\n    if (!isCrossLinkingEnabled()) return null;\n    const baseURL = DOMAINS.APK_LATEST;\n    // Use smart mapping service for better matching\n    return (0,_toolMappingService__WEBPACK_IMPORTED_MODULE_0__.generateSmartAPKURL)(toolDetails, baseURL);\n};\n/**\n * Generate older versions APK download URL for an AI tool\n */ const generateOlderVersionsAPKURL = (toolDetails)=>{\n    if (!isCrossLinkingEnabled()) return null;\n    const baseURL = DOMAINS.APK_ALL_VERSIONS;\n    // Use smart mapping service for better matching\n    return (0,_toolMappingService__WEBPACK_IMPORTED_MODULE_0__.generateSmartAPKURL)(toolDetails, baseURL);\n};\n/**\n * Generate APK download URL for an AI tool (Legacy - defaults to latest)\n */ const generateAPKURL = (toolDetails)=>{\n    return generateLatestAPKURL(toolDetails);\n};\n/**\n * Generate AI tools discovery URL for an app\n */ const generateAIToolsURL = (appDetails)=>{\n    if (!isCrossLinkingEnabled()) return null;\n    const baseURL = DOMAINS.AI_TOOLS_SITE;\n    // If we have app category, link to relevant AI tools category\n    if (appDetails?.category) {\n        // Reverse mapping from APK to AI categories\n        const apkCategory = appDetails.category.toLowerCase();\n        let relevantCategory = \"productivity\"; // default\n        // Find matching AI category\n        for (const [aiCat, apkCats] of Object.entries(AI_TO_APK_MAPPING)){\n            if (apkCats.includes(apkCategory)) {\n                relevantCategory = aiCat;\n                break;\n            }\n        }\n        return `${baseURL}/tools/${relevantCategory}?ref=apk&app=${encodeURIComponent(appDetails.appId || \"\")}`;\n    }\n    // Fallback to main AI tools page\n    return `${baseURL}/tool?ref=apk&app=${encodeURIComponent(appDetails?.appId || \"\")}`;\n};\n/**\n * Generate SEO-friendly anchor text for cross-links\n */ const generateAnchorText = (type, details)=>{\n    if (type === \"ai-tools\") {\n        const category = details?.category || \"productivity\";\n        return `Discover AI ${category.charAt(0).toUpperCase() + category.slice(1)} Tools`;\n    } else if (type === \"apk-download\") {\n        const toolName = details?.title || \"Related Apps\";\n        return `Download ${toolName} APK & Similar Apps`;\n    }\n    return \"Explore More\";\n};\n/**\n * Track cross-link clicks for analytics\n */ const trackCrossLinkClick = (source, target, details)=>{\n    if (false) {}\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/utils/crossLinking.js\n");

/***/ }),

/***/ "(ssr)/./src/app/utils/toolMappingService.js":
/*!*********************************************!*\
  !*** ./src/app/utils/toolMappingService.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TOOL_APP_MAPPINGS: () => (/* binding */ TOOL_APP_MAPPINGS),\n/* harmony export */   generateSmartAPKURL: () => (/* binding */ generateSmartAPKURL),\n/* harmony export */   getBestAPKCategory: () => (/* binding */ getBestAPKCategory),\n/* harmony export */   getRecommendedApps: () => (/* binding */ getRecommendedApps),\n/* harmony export */   getRelevantAPKCategories: () => (/* binding */ getRelevantAPKCategories)\n/* harmony export */ });\n/**\n * Intelligent AI-Tools-to-App Mapping Service\n * Provides smart matching between AI tools and APK apps\n */ /**\n * Enhanced AI-tool-to-app mappings with keyword matching\n */ const TOOL_APP_MAPPINGS = {\n    // Direct tool name mappings to specific apps\n    direct: {\n        \"chatgpt\": [\n            \"com.openai.chatgpt\",\n            \"ai.chat.gpt.bot\"\n        ],\n        \"claude\": [\n            \"com.anthropic.claude\"\n        ],\n        \"gemini\": [\n            \"com.google.android.apps.bard\",\n            \"com.google.android.apps.assistant\"\n        ],\n        \"copilot\": [\n            \"com.microsoft.copilot\",\n            \"com.microsoft.office.outlook\"\n        ],\n        \"midjourney\": [\n            \"com.midjourney.android\",\n            \"com.adobe.photoshop.express\"\n        ],\n        \"canva\": [\n            \"com.canva.editor\"\n        ],\n        \"photoshop\": [\n            \"com.adobe.photoshop.express\",\n            \"com.adobe.psmobile\"\n        ],\n        \"lightroom\": [\n            \"com.adobe.lrmobile\"\n        ],\n        \"figma\": [\n            \"com.figma.mirror\"\n        ],\n        \"notion\": [\n            \"notion.id\"\n        ],\n        \"slack\": [\n            \"com.Slack\"\n        ],\n        \"discord\": [\n            \"com.discord\"\n        ],\n        \"zoom\": [\n            \"us.zoom.videomeetings\"\n        ],\n        \"teams\": [\n            \"com.microsoft.teams\"\n        ],\n        \"telegram\": [\n            \"org.telegram.messenger\"\n        ],\n        \"whatsapp\": [\n            \"com.whatsapp\"\n        ],\n        \"instagram\": [\n            \"com.instagram.android\"\n        ],\n        \"tiktok\": [\n            \"com.zhiliaoapp.musically\"\n        ],\n        \"youtube\": [\n            \"com.google.android.youtube\"\n        ],\n        \"spotify\": [\n            \"com.spotify.music\"\n        ],\n        \"netflix\": [\n            \"com.netflix.mediaclient\"\n        ],\n        \"gmail\": [\n            \"com.google.android.gm\"\n        ],\n        \"outlook\": [\n            \"com.microsoft.office.outlook\"\n        ],\n        \"chrome\": [\n            \"com.android.chrome\"\n        ],\n        \"firefox\": [\n            \"org.mozilla.firefox\"\n        ],\n        \"office\": [\n            \"com.microsoft.office.word\",\n            \"com.microsoft.office.excel\"\n        ],\n        \"word\": [\n            \"com.microsoft.office.word\"\n        ],\n        \"excel\": [\n            \"com.microsoft.office.excel\"\n        ],\n        \"powerpoint\": [\n            \"com.microsoft.office.powerpoint\"\n        ],\n        \"adobe\": [\n            \"com.adobe.photoshop.express\",\n            \"com.adobe.lrmobile\"\n        ],\n        \"grammarly\": [\n            \"com.grammarly.android.keyboard\"\n        ],\n        \"translate\": [\n            \"com.google.android.apps.translate\"\n        ],\n        \"maps\": [\n            \"com.google.android.apps.maps\"\n        ],\n        \"calendar\": [\n            \"com.google.android.calendar\"\n        ],\n        \"notes\": [\n            \"com.google.android.keep\",\n            \"com.samsung.android.app.notes\"\n        ],\n        \"weather\": [\n            \"com.google.android.apps.weather\",\n            \"com.weather.Weather\"\n        ],\n        \"calculator\": [\n            \"com.google.android.calculator\",\n            \"com.samsung.android.calculator\"\n        ],\n        \"camera\": [\n            \"com.google.android.GoogleCamera\",\n            \"com.samsung.android.camera2\"\n        ],\n        \"gallery\": [\n            \"com.google.android.apps.photos\",\n            \"com.samsung.android.gallery3d\"\n        ],\n        \"music\": [\n            \"com.google.android.music\",\n            \"com.samsung.android.music\"\n        ],\n        \"video\": [\n            \"com.google.android.videos\",\n            \"com.samsung.android.video\"\n        ]\n    },\n    // Category-based mappings\n    category: {\n        \"social-media\": [\n            \"communication\",\n            \"social\"\n        ],\n        \"email-generator\": [\n            \"communication\",\n            \"productivity\"\n        ],\n        \"writing-generator\": [\n            \"productivity\",\n            \"education\"\n        ],\n        \"blog-generator\": [\n            \"news-magazines\",\n            \"productivity\"\n        ],\n        \"business\": [\n            \"business\",\n            \"productivity\"\n        ],\n        \"productivity\": [\n            \"productivity\",\n            \"tools\"\n        ],\n        \"automation\": [\n            \"tools\",\n            \"productivity\"\n        ],\n        \"art\": [\n            \"art-design\",\n            \"photography\"\n        ],\n        \"graphic-design\": [\n            \"art-design\",\n            \"photography\"\n        ],\n        \"image-generation\": [\n            \"art-design\",\n            \"photography\"\n        ],\n        \"video-generation\": [\n            \"video-players\",\n            \"entertainment\"\n        ],\n        \"music\": [\n            \"music-audio\",\n            \"entertainment\"\n        ],\n        \"education\": [\n            \"education\",\n            \"books-reference\"\n        ],\n        \"research\": [\n            \"education\",\n            \"books-reference\"\n        ],\n        \"health\": [\n            \"health-fitness\",\n            \"medical\"\n        ],\n        \"lifestyle\": [\n            \"lifestyle\",\n            \"health-fitness\"\n        ],\n        \"finace\": [\n            \"finance\",\n            \"business\"\n        ],\n        \"startup\": [\n            \"business\",\n            \"productivity\"\n        ],\n        \"marketing\": [\n            \"business\",\n            \"social\"\n        ],\n        \"developer-tools\": [\n            \"tools\",\n            \"libraries-demo\"\n        ],\n        \"communication\": [\n            \"communication\",\n            \"social\"\n        ],\n        \"translation\": [\n            \"education\",\n            \"tools\"\n        ],\n        \"avatar\": [\n            \"personalization\",\n            \"art-design\"\n        ],\n        \"anime\": [\n            \"entertainment\",\n            \"art-design\"\n        ],\n        \"gaming\": [\n            \"games\",\n            \"entertainment\"\n        ],\n        \"transcription\": [\n            \"productivity\",\n            \"tools\"\n        ],\n        \"audio\": [\n            \"music-audio\",\n            \"tools\"\n        ],\n        \"podcast\": [\n            \"music-audio\",\n            \"news-magazines\"\n        ],\n        \"presentation\": [\n            \"productivity\",\n            \"business\"\n        ],\n        \"document-generation\": [\n            \"productivity\",\n            \"business\"\n        ],\n        \"note-taking\": [\n            \"productivity\",\n            \"education\"\n        ],\n        \"organization\": [\n            \"productivity\",\n            \"lifestyle\"\n        ],\n        \"scheduling\": [\n            \"productivity\",\n            \"lifestyle\"\n        ],\n        \"monitoring\": [\n            \"health-fitness\",\n            \"tools\"\n        ],\n        \"analysis\": [\n            \"business\",\n            \"tools\"\n        ],\n        \"security\": [\n            \"tools\",\n            \"productivity\"\n        ],\n        \"privacy\": [\n            \"tools\",\n            \"productivity\"\n        ],\n        \"optimization\": [\n            \"tools\",\n            \"productivity\"\n        ],\n        \"customization\": [\n            \"personalization\",\n            \"tools\"\n        ],\n        \"navigation\": [\n            \"maps-navigation\",\n            \"travel-local\"\n        ],\n        \"travel\": [\n            \"travel-local\",\n            \"lifestyle\"\n        ],\n        \"e-commerce\": [\n            \"shopping\",\n            \"business\"\n        ],\n        \"recommendation\": [\n            \"shopping\",\n            \"lifestyle\"\n        ],\n        \"information\": [\n            \"news-magazines\",\n            \"education\"\n        ],\n        \"prediction\": [\n            \"tools\",\n            \"lifestyle\"\n        ]\n    },\n    // Keyword-based mappings for tool descriptions/titles\n    keywords: {\n        \"chat\": [\n            \"communication\",\n            \"social\"\n        ],\n        \"message\": [\n            \"communication\",\n            \"social\"\n        ],\n        \"photo\": [\n            \"art-design\",\n            \"photography\"\n        ],\n        \"image\": [\n            \"art-design\",\n            \"photography\"\n        ],\n        \"video\": [\n            \"video-players\",\n            \"entertainment\"\n        ],\n        \"music\": [\n            \"music-audio\",\n            \"entertainment\"\n        ],\n        \"edit\": [\n            \"art-design\",\n            \"video-players\"\n        ],\n        \"design\": [\n            \"art-design\",\n            \"productivity\"\n        ],\n        \"write\": [\n            \"productivity\",\n            \"education\"\n        ],\n        \"text\": [\n            \"productivity\",\n            \"education\"\n        ],\n        \"document\": [\n            \"productivity\",\n            \"business\"\n        ],\n        \"note\": [\n            \"productivity\",\n            \"education\"\n        ],\n        \"task\": [\n            \"productivity\",\n            \"business\"\n        ],\n        \"calendar\": [\n            \"productivity\",\n            \"lifestyle\"\n        ],\n        \"schedule\": [\n            \"productivity\",\n            \"lifestyle\"\n        ],\n        \"email\": [\n            \"communication\",\n            \"productivity\"\n        ],\n        \"translate\": [\n            \"education\",\n            \"tools\"\n        ],\n        \"language\": [\n            \"education\",\n            \"tools\"\n        ],\n        \"learn\": [\n            \"education\",\n            \"books-reference\"\n        ],\n        \"fitness\": [\n            \"health-fitness\",\n            \"lifestyle\"\n        ],\n        \"health\": [\n            \"health-fitness\",\n            \"medical\"\n        ],\n        \"game\": [\n            \"games\",\n            \"entertainment\"\n        ],\n        \"social\": [\n            \"social\",\n            \"communication\"\n        ],\n        \"business\": [\n            \"business\",\n            \"productivity\"\n        ],\n        \"finance\": [\n            \"finance\",\n            \"business\"\n        ],\n        \"money\": [\n            \"finance\",\n            \"business\"\n        ],\n        \"shop\": [\n            \"shopping\",\n            \"lifestyle\"\n        ],\n        \"buy\": [\n            \"shopping\",\n            \"lifestyle\"\n        ],\n        \"travel\": [\n            \"travel-local\",\n            \"lifestyle\"\n        ],\n        \"news\": [\n            \"news-magazines\",\n            \"education\"\n        ],\n        \"weather\": [\n            \"weather\",\n            \"lifestyle\"\n        ],\n        \"security\": [\n            \"tools\",\n            \"productivity\"\n        ],\n        \"clean\": [\n            \"tools\",\n            \"productivity\"\n        ],\n        \"optimize\": [\n            \"tools\",\n            \"productivity\"\n        ],\n        \"backup\": [\n            \"tools\",\n            \"productivity\"\n        ],\n        \"scan\": [\n            \"tools\",\n            \"productivity\"\n        ],\n        \"voice\": [\n            \"music-audio\",\n            \"communication\"\n        ],\n        \"speech\": [\n            \"music-audio\",\n            \"communication\"\n        ],\n        \"ai\": [\n            \"tools\",\n            \"productivity\"\n        ],\n        \"smart\": [\n            \"tools\",\n            \"productivity\"\n        ],\n        \"auto\": [\n            \"tools\",\n            \"productivity\"\n        ],\n        \"generator\": [\n            \"productivity\",\n            \"tools\"\n        ],\n        \"creator\": [\n            \"art-design\",\n            \"productivity\"\n        ],\n        \"maker\": [\n            \"art-design\",\n            \"productivity\"\n        ],\n        \"builder\": [\n            \"productivity\",\n            \"tools\"\n        ]\n    }\n};\n/**\n * Get relevant APK categories for an AI tool\n */ const getRelevantAPKCategories = (toolDetails)=>{\n    const categories = new Set();\n    if (!toolDetails) return [\n        \"productivity\"\n    ];\n    const toolId = (toolDetails.appId || \"\").toLowerCase();\n    const title = (toolDetails.title || \"\").toLowerCase();\n    const category = (toolDetails.subCategory || \"\").toLowerCase();\n    const description = (toolDetails.overview || toolDetails.summary || \"\").toLowerCase();\n    // Check direct tool mappings\n    for (const [toolName, apkCategories] of Object.entries(TOOL_APP_MAPPINGS.direct)){\n        if (toolId.includes(toolName) || title.includes(toolName)) {\n            // For direct mappings, we get specific app IDs, so we map to general categories\n            categories.add(\"productivity\");\n            categories.add(\"tools\");\n        }\n    }\n    // Check category mappings\n    const normalizedCategory = category.replace(/[^a-z0-9]/g, \"-\");\n    if (TOOL_APP_MAPPINGS.category[normalizedCategory]) {\n        TOOL_APP_MAPPINGS.category[normalizedCategory].forEach((cat)=>categories.add(cat));\n    }\n    // Check keyword mappings\n    const searchText = `${title} ${description}`.toLowerCase();\n    for (const [keyword, apkCategories] of Object.entries(TOOL_APP_MAPPINGS.keywords)){\n        if (searchText.includes(keyword)) {\n            apkCategories.forEach((cat)=>categories.add(cat));\n        }\n    }\n    // Return array with most relevant first, fallback to productivity\n    const result = Array.from(categories);\n    return result.length > 0 ? result : [\n        \"productivity\"\n    ];\n};\n/**\n * Get specific app recommendations for an AI tool\n */ const getRecommendedApps = (toolDetails)=>{\n    const apps = new Set();\n    if (!toolDetails) return [];\n    const toolId = (toolDetails.appId || \"\").toLowerCase();\n    const title = (toolDetails.title || \"\").toLowerCase();\n    // Check direct tool mappings for specific apps\n    for (const [toolName, appIds] of Object.entries(TOOL_APP_MAPPINGS.direct)){\n        if (toolId.includes(toolName) || title.includes(toolName)) {\n            appIds.forEach((appId)=>apps.add(appId));\n        }\n    }\n    return Array.from(apps);\n};\n/**\n * Get the best APK category for an AI tool\n */ const getBestAPKCategory = (toolDetails)=>{\n    const categories = getRelevantAPKCategories(toolDetails);\n    return categories[0] || \"productivity\";\n};\n/**\n * Generate smart APK URL with best matching category or specific app\n */ const generateSmartAPKURL = (toolDetails, baseURL)=>{\n    const recommendedApps = getRecommendedApps(toolDetails);\n    const toolId = toolDetails?.appId || \"\";\n    const toolTitle = toolDetails?.title || \"\";\n    // If we have a specific app recommendation, link to it\n    if (recommendedApps.length > 0) {\n        const primaryApp = recommendedApps[0];\n        return `${baseURL}/apps/appdetails/${primaryApp}?ref=ai-tools&tool=${encodeURIComponent(toolId)}&title=${encodeURIComponent(toolTitle)}`;\n    }\n    // Otherwise, link to the best category\n    const bestCategory = getBestAPKCategory(toolDetails);\n    return `${baseURL}/apps/${bestCategory}?ref=ai-tools&tool=${encodeURIComponent(toolId)}&title=${encodeURIComponent(toolTitle)}`;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/utils/toolMappingService.js\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"04a81f5727e4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdG9vbHBsYXRlLmFpLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz8yODA0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDRhODFmNTcyN2U0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/Ads.jsx":
/*!*************************!*\
  !*** ./src/app/Ads.jsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\Anchoring2\instadownloader\src\app\Ads.jsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/components/UniversalNavigation.jsx":
/*!****************************************************!*\
  !*** ./src/app/components/UniversalNavigation.jsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CrossSiteBanner: () => (/* binding */ e0),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\Anchoring2\instadownloader\src\app\components\UniversalNavigation.jsx#CrossSiteBanner`);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\Anchoring2\instadownloader\src\app\components\UniversalNavigation.jsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/components/navbar/Navbar.jsx":
/*!**********************************************!*\
  !*** ./src/app/components/navbar/Navbar.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\Anchoring2\instadownloader\src\app\components\navbar\Navbar.jsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_js_import_arguments_src_fonts_GeistVF_woff_variable_font_geist_sans_weight_100_900_variableName_geistSans___WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/font/local/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/GeistVF.woff\",\"variable\":\"--font-geist-sans\",\"weight\":\"100 900\"}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/local/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"\\\",\\\"arguments\\\":[{\\\"src\\\":\\\"./fonts/GeistVF.woff\\\",\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"weight\\\":\\\"100 900\\\"}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_js_import_arguments_src_fonts_GeistVF_woff_variable_font_geist_sans_weight_100_900_variableName_geistSans___WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_font_local_target_css_path_src_app_layout_js_import_arguments_src_fonts_GeistVF_woff_variable_font_geist_sans_weight_100_900_variableName_geistSans___WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_js_import_arguments_src_fonts_GeistMonoVF_woff_variable_font_geist_mono_weight_100_900_variableName_geistMono___WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/font/local/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/GeistMonoVF.woff\",\"variable\":\"--font-geist-mono\",\"weight\":\"100 900\"}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/local/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"\\\",\\\"arguments\\\":[{\\\"src\\\":\\\"./fonts/GeistMonoVF.woff\\\",\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"weight\\\":\\\"100 900\\\"}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_js_import_arguments_src_fonts_GeistMonoVF_woff_variable_font_geist_mono_weight_100_900_variableName_geistMono___WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_font_local_target_css_path_src_app_layout_js_import_arguments_src_fonts_GeistMonoVF_woff_variable_font_geist_mono_weight_100_900_variableName_geistMono___WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _fortawesome_fontawesome_svg_core_styles_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/fontawesome-svg-core/styles.css */ \"(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/styles.css\");\n/* harmony import */ var _fortawesome_fontawesome_svg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/fontawesome-svg-core */ \"(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/index.mjs\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_navbar_Navbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/navbar/Navbar */ \"(rsc)/./src/app/components/navbar/Navbar.jsx\");\n/* harmony import */ var _Ads__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Ads */ \"(rsc)/./src/app/Ads.jsx\");\n/* harmony import */ var _components_UniversalNavigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/UniversalNavigation */ \"(rsc)/./src/app/components/UniversalNavigation.jsx\");\n\n\n\n\n\n\n_fortawesome_fontawesome_svg_core__WEBPACK_IMPORTED_MODULE_2__.config.autoAddCss = false;\n\n\n\n\n\nconst isAdsServe = JSON.parse(\"true\");\nconst metadata = {\n    title: \"SmartTools | Discover Top AI Tools & Reviews | Your AI Resource\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"stylesheet\",\n                        href: \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/st_logo.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        strategy: \"afterInteractive\",\n                        src: \"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2219975169694529\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_local_target_css_path_src_app_layout_js_import_arguments_src_fonts_GeistVF_woff_variable_font_geist_sans_weight_100_900_variableName_geistSans___WEBPACK_IMPORTED_MODULE_9___default().variable)} ${(next_font_local_target_css_path_src_app_layout_js_import_arguments_src_fonts_GeistMonoVF_woff_variable_font_geist_mono_weight_100_900_variableName_geistMono___WEBPACK_IMPORTED_MODULE_10___default().variable)} antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navbar_Navbar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    children,\n                    isAdsServe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Ads__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            adSlot: \"5991868361\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 50,\n                        columnNumber: 26\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-white dark:bg-gray-900\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto w-full max-w-screen-xl p-4 py-6 lg:py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:flex md:justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full md:w-6/12 lg:w-6/12 mb-6 md:mb-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"https://smart-tools.com/\",\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: \"/st_logo.png\",\n                                                            className: \"h-8 me-3\",\n                                                            alt: \"SmartTool Logo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 60,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"self-center text-2xl font-semibold whitespace-nowrap dark:text-white\",\n                                                            children: \"SmartTools\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 61,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 59,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"lg:me-6 p-1 dark:text-gray-400\",\n                                                    children: \"SmartTools is an AI tools platform featuring 1000+ tool reviews and value-packed blogs targeted for professionals to increase everyone's productivity and efficiency.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-8 sm:gap-6 sm:grid-cols-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UniversalNavigation__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    variant: \"footer\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white\",\n                                                            children: \"Pages\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 72,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-gray-500 dark:text-gray-400 font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"mb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        href: \"/\",\n                                                                        className: \"hover:underline\",\n                                                                        children: \"Home\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                        lineNumber: 75,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                    lineNumber: 74,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        href: \"/tool\",\n                                                                        className: \"hover:underline\",\n                                                                        children: \"Tools\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                        lineNumber: 78,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                    lineNumber: 77,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 73,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white\",\n                                                            children: \"Legal\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 83,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-gray-500 dark:text-gray-400 font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"mb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        href: \"/privacy-policy\",\n                                                                        className: \"hover:underline\",\n                                                                        children: \"Privacy Policy\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                        lineNumber: 86,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                    lineNumber: 85,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        href: \"terms-conditions\",\n                                                                        className: \"hover:underline\",\n                                                                        children: \"Terms & Conditions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                        lineNumber: 89,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                    lineNumber: 88,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 84,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white\",\n                                                            children: \"Categories\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 94,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-gray-500 dark:text-gray-400 font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"mb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        href: \"/\",\n                                                                        className: \"hover:underline\",\n                                                                        children: \"AI Audio Tools\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                        lineNumber: 97,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                    lineNumber: 96,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"mb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        href: \"/\",\n                                                                        className: \"hover:underline\",\n                                                                        children: \"AI Image Tools\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                        lineNumber: 100,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                    lineNumber: 99,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"mb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        href: \"/\",\n                                                                        className: \"hover:underline\",\n                                                                        children: \"AI Video Tools\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                        lineNumber: 103,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                    lineNumber: 102,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 95,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                    className: \"my-6 border-gray-200 sm:mx-auto dark:border-gray-700 lg:my-8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sm:flex sm:items-center sm:justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500 sm:text-center dark:text-gray-400\",\n                                            children: [\n                                                \"\\xa9 2024 \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"https://smart-tools.com/\",\n                                                    className: \"hover:underline\",\n                                                    children: \"SmartTools\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \". All Rights Reserved.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex mt-4 sm:justify-center sm:mt-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"#\",\n                                                    className: \"text-gray-500 hover:text-gray-900 dark:hover:text-white\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fa-brands fa-facebook\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 116,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sr-only\",\n                                                            children: \"Facebook page\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 117,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"#\",\n                                                    className: \"text-gray-500 hover:text-gray-900 dark:hover:text-white ms-5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fa-brands fa-discord\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sr-only\",\n                                                            children: \"Discord community\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"#\",\n                                                    className: \"text-gray-500 hover:text-gray-900 dark:hover:text-white ms-5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fa-brands fa-twitter\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sr-only\",\n                                                            children: \"Twitter page\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"#\",\n                                                    className: \"text-gray-500 hover:text-gray-900 dark:hover:text-white ms-5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fa-brands fa-github\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sr-only\",\n                                                            children: \"GitHub account\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"#\",\n                                                    className: \"text-gray-500 hover:text-gray-900 dark:hover:text-white ms-5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fa-brands fa-dribbble\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sr-only\",\n                                                            children: \"Dribbble account\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring2\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@fortawesome","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring2%5Cinstadownloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring2%5Cinstadownloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();