import connectDB from "../../database/mongoose";
import { NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import User from "@/app/database/user";

export async function POST(req) {
  const { password, email } = await req.json();

  await connectDB();

  const userExists = await User.findOne({ email });
  const hashedPassword = await bcrypt.hash(password, 10);
  userExists.set({ password: hashedPassword });

  userExists.resetToken = undefined;
  userExists.resetTokenExpiry = undefined;

  try {
    await userExists.save();
    return new NextResponse("Your password updated Successfully !!", {
      status: 200,
    });
    
  } catch (e) {
    console.log(e, "error");
    return new NextResponse(e, { status: 500 });
  }
}
