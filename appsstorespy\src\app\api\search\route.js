import { NextResponse } from "next/server";
import gplay from "google-play-scraper";
const store = require("app-store-scraper");
import connectDB from "../../database/mongoose";
import App from "../../database/appData";
import AppId from "../../database/appId";
import { getServerSession } from "next-auth";

export const POST = async (request, response) => {
  try {
    const session = await getServerSession(request);
    await connectDB();
    const { searchTerm, price, storeType, countryCode } = await request.json();

    if (!searchTerm || searchTerm.trim() === "") {
      return NextResponse.json(
        { error: "Search term is required" },
        { status: 400 }
      );
    }
    let data;
    const appIdPattern = /^[a-zA-Z][a-zA-Z0-9_]*(\.[a-zA-Z][a-zA-Z0-9_]*)+$/;
    if (storeType === "playStore" && appIdPattern.test(searchTerm)) {
      data = [await gplay.app({ appId: searchTerm })];
    } else if(storeType === "playStore") {
      data = await gplay.search({
        term: searchTerm,
        price: price,
        num: 250,
        country: countryCode || "US",
        fullDetail: true,
      });
    } else if (storeType === "appStore") {
      data = await store?.search({
        term: searchTerm,
        price: price,
        num: 50,
      });
    }
    if (data && data.length > 0) {
      const savedApps = await Promise.all(
        data.map(async (appData) => {
          const { appId } = appData;

          try {
            await AppId.findOneAndUpdate(
              { appId },
              { appId },
              { upsert: true }
            );
          } catch (error) {
            console.error("Error updating AppId collection:", error);
          }

          try {
            // Find and update/create app data in App collection
            const existingAppData = await App.findOne({ appId });
            let savedApp;

            if (existingAppData) {
              // Update only specific fields in the existing document
              existingAppData.set(appData);
              savedApp = await existingAppData.save();
            } else {
              savedApp = await App.create(appData);
            }

            return savedApp;
          } catch (error) {
            console.error("Error updating App collection:", error);
            return null;
          }
        })
      );
      const successfulApps = savedApps.filter((app) => app !== null);
      const filteredApps = successfulApps.map(app => ({
        title: app.title,
        appId: app.appId,
        genre: app.genre,
        icon:app.icon,
        developer:app.developer,
        scoreText:app.scoreText,
        free:app.free,
        released:app.released,
        updated:app.updated,
        maxInstalls:app.maxInstalls,
        ratings:app.ratings,
        reviews:app.reviews,
        adSupported: session ? app.adSupported : undefined
      }));
      return NextResponse.json({
        message: "available apps",
        savedApps: filteredApps,
      });
    } else {
      return NextResponse.json({ error: "No app found" }, { status: 400 });
    }
  } catch (error) {
    console.error("Error:", error);
    if (error.status === 404) {
      return NextResponse.json(
        { message: "App not found", error },
        { status: 202 }
      );
    }else{
      return NextResponse.json(
        { message: "Error fetching or saving app details", error },
        { status: 500 }
      );
    }
  }
};
