export const formatInstalls = (installs) => {
  if (installs >= 1000000000) {
    return Math.floor(installs / 1000000000) + "B+";
  } else if (installs >= 1000000) {
    return Math.floor(installs / 1000000) + "M+";
  } else if (installs >= 1000) {
    return Math.floor(installs / 1000) + "K+";
  } else {
    return installs;
  }
};

export const formatRatingsAndReviews = (ratings) => {
  if (ratings >= 1000000000) {
    return (Math.floor(ratings / 10000000) / 100).toFixed(1) + "B+";
  } else if (ratings >= 1000000) {
    return (Math.floor(ratings / 10000) / 100).toFixed(1) + "M+";
  } else if (ratings >= 1000) {
    return Math.floor(ratings / 1000) + "K+";
  } else {
    return ratings;
  }
};

export const formatReleasedDate = (releaseDate, isFormat=false) => {
  const releaseTimestamp = new Date(releaseDate);
  const currentTimestamp = new Date();
  const timeDifference = currentTimestamp - releaseTimestamp;

  const years = Math.floor(timeDifference / (365 * 24 * 60 * 60 * 1000));

  const formattedDate = releaseTimestamp.toLocaleDateString("en-GB", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });

  return  isFormat ? formattedDate: `${years} years ago (${formattedDate})`;
};

export const formatUpdateDate = (timestamp, isFormat) => {
  const date = new Date(Number(timestamp));
  const currentDate = new Date();

  const timeDifference = currentDate - date;
  const daysDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24));

  const day = date.getDate();
  const month = new Intl.DateTimeFormat("en", { month: "short" }).format(date);
  const year = date.getFullYear();
  const formattedDate = `${day} ${month} ${year}`;

  const result =  isFormat ? formattedDate: `${daysDifference} days ago (${formattedDate})`;

  return result;
};

