"use client";
import React, { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useRouter } from "next/navigation";
import {
  AppBar,
  Toolbar,
  Button,
  Typography,
  Box,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemText,
  Divider,
  Avatar,
  Menu,
  MenuItem
} from "@mui/material";
import MenuIcon from "@mui/icons-material/Menu";
import SearchIcon from "@mui/icons-material/Search";
import AccountCircle from "@mui/icons-material/AccountCircle";
import CloseIcon from "@mui/icons-material/Close";
import Swal from "sweetalert2";
import { useSession, signOut } from "next-auth/react";
import Popover from '@mui/material/Popover';
import PopupState, { bindTrigger, bindPopover } from 'material-ui-popup-state';
import SearchApps from "../searchbar/SearchApps";

const Navbar = () => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [scrolled, setScrolled] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const { data: session, status } = useSession();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogOut = () => {
    Swal.fire({
      title: "Do you want to Logout?",
      showCancelButton: true,
      confirmButtonText: "Yes",
      icon: "warning",
      showCloseButton: true,
    }).then((result) => {
      if (result.isConfirmed) {
        signOut({ callbackUrl: process.env.NEXTAUTH_URL });
        Swal.fire({
          title: "Logout!",
          text: "You Logout successfully.",
          icon: "success",
        });
      }
    });
  };

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const menuId = 'primary-search-account-menu';


  const renderMenu = (
    <Menu
      anchorEl={anchorEl}
      anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      id={menuId}
      keepMounted
      transformOrigin={{ vertical: 'top', horizontal: 'right' }}
      open={Boolean(anchorEl)}
      onClose={handleMenuClose}
    >
      <MenuItem onClick={handleMenuClose}>
        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
          {session?.user?.email}
        </Typography>
      </MenuItem>
      <Divider />
      <MenuItem onClick={() => { router.push("/profile"); handleMenuClose(); }}>
        Profile
      </MenuItem>
      <MenuItem onClick={handleLogOut}>Logout</MenuItem>
    </Menu>
  );

  const drawer = (
    <Box
      onClick={handleDrawerToggle}
      sx={{
        width: 280,
        height: '100%',
        backgroundColor: '#f8fafc',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 2,
          borderBottom: '1px solid rgba(0, 0, 0, 0.12)'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box
            sx={{
              width: 36,
              height: 36,
              backgroundColor: '#00A3FF',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 'bold',
              fontSize: '1.1rem',
              boxShadow: '0 2px 8px rgba(0, 163, 255, 0.3)'
            }}
          >
            AS
          </Box>
          <Typography
            variant="h6"
            sx={{
              color: '#00A3FF',
              fontWeight: 800,
              fontFamily: "'Poppins', sans-serif",
              letterSpacing: '0.5px'
            }}
          >
            AppsStoreSpy
          </Typography>
        </Box>
        <IconButton
          onClick={handleDrawerToggle}
          sx={{
            color: '#666',
            '&:hover': {
              backgroundColor: 'rgba(0, 163, 255, 0.1)',
              color: '#00A3FF'
            }
          }}
        >
          <CloseIcon />
        </IconButton>
      </Box>

      {/* User Info Section */}
      {status === "authenticated" && (
        <Box
          sx={{
            p: 2,
            borderBottom: '1px solid rgba(0, 0, 0, 0.08)',
            backgroundColor: 'rgba(0, 163, 255, 0.05)'
          }}
        >
          <Typography
            variant="subtitle2"
            sx={{
              fontWeight: 600,
              color: '#333',
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}
          >
            <AccountCircle sx={{ color: '#00A3FF' }} />
            {session.user.email}
          </Typography>
        </Box>
      )}


      {/* Navigation Links */}
      <Box sx={{ flex: 1, overflowY: 'auto', p: 2 }}>
        <List>
          {[
            { text: 'Home', path: '/' },
            // { text: 'Optimization', path: '/optimization' },
            { text: 'Top Apps', path: '/top-apps' },
            { text: 'Top Games', path: '/top-games' },
            // { text: 'Pricing', path: '/pricing' },
          ].map((item) => (
            <ListItem
              key={item.text}
              button
              component={Link}
              href={item.path}
              selected={pathname === item.path}
              sx={{
                borderRadius: '8px',
                mb: 0.5,
                '&.Mui-selected': {
                  backgroundColor: 'rgba(0, 163, 255, 0.1)',
                  color: '#00A3FF',
                  '&:hover': {
                    backgroundColor: 'rgba(0, 163, 255, 0.15)'
                  }
                },
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.04)'
                }
              }}
            >
              <ListItemText
                primary={item.text}
                primaryTypographyProps={{
                  fontWeight: pathname === item.path ? 600 : 500
                }}
              />
            </ListItem>
          ))}
        </List>
      </Box>

      {/* Auth Buttons */}
      <Box sx={{ p: 2, borderTop: '1px solid rgba(0, 0, 0, 0.08)' }}>
        {status === "authenticated" ? (
          <Button
            fullWidth
            variant="contained"
            sx={{
              backgroundColor: '#00A3FF',
              '&:hover': {
                backgroundColor: '#0088cc',
                boxShadow: '0 2px 12px rgba(0, 163, 255, 0.4)'
              },
              py: 1.5,
              borderRadius: '8px',
              fontWeight: 600,
              textTransform: 'none'
            }}
            onClick={handleLogOut}
          >
            Logout
          </Button>
        ) : (
          <>
            <Button
              fullWidth
              variant="contained"
              sx={{
                backgroundColor: '#00A3FF',
                '&:hover': {
                  backgroundColor: '#0088cc',
                  boxShadow: '0 2px 12px rgba(0, 163, 255, 0.4)'
                },
                py: 1.5,
                borderRadius: '8px',
                fontWeight: 600,
                textTransform: 'none',
                mb: 1
              }}
              onClick={() => router.push("/login")}
            >
              Login
            </Button>
            <Button
              fullWidth
              variant="outlined"
              sx={{
                color: '#00A3FF',
                borderColor: '#00A3FF',
                '&:hover': {
                  borderColor: '#0088cc',
                  backgroundColor: 'rgba(0, 163, 255, 0.05)'
                },
                py: 1.5,
                borderRadius: '8px',
                fontWeight: 600,
                textTransform: 'none'
              }}
              onClick={() => router.push("/register")}
            >
              Sign Up
            </Button>
          </>
        )}
      </Box>
    </Box>
  );

  return (
    <Box sx={{ flexGrow: 1 }}>
      <AppBar
        position="fixed"
        sx={{
          backgroundColor: scrolled ? 'rgba(235, 243, 255, 0.95)' : '#ebf3ff',
          backdropFilter: scrolled ? 'blur(10px)' : 'none',
          transition: 'all 0.3s ease',
          boxShadow: scrolled ? '0 2px 10px rgba(0, 0, 0, 0.1)' : 'none',
        }}
      >
        <Toolbar>
          <Box sx={{ display: { xs: 'block', md: 'none' },width:"100%" }}>

            <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%', alignItems: 'center' }}>
              {/* Left Side - Menu Button (Mobile Only) */}
              <IconButton
                size="large"
                edge="start"
                color="inherit"
                aria-label="open drawer"
                sx={{
                  mr: 2,
                  display: { md: 'none' },
                  color: "#00A3FF"
                }}
                onClick={handleDrawerToggle}
              >
                <MenuIcon />
              </IconButton>

              {/* Right Side - Search Button (Mobile Only) */}
              <PopupState variant="popover" popupId="demo-popup-popover">
                {(popupState) => (
                  <div>
                    <IconButton
                      {...bindTrigger(popupState)}
                      size="large"
                      aria-label="search"
                      color="inherit"
                      sx={{
                        display: { md: 'none' },
                        ml: 2,
                        background: "#00A3FF",
                        "&:hover": {
                          backgroundColor: "#00A3FF",
                          boxShadow: "0 0 10px 3px rgba(0, 163, 255, 0.5)",
                          transform: "scale(1.1)",
                        },
                        transition: "all 0.2s ease-in-out",
                        color: "white"
                      }}
                    >
                      <SearchIcon />
                    </IconButton>
                    <Popover
                      {...bindPopover(popupState)}
                      anchorOrigin={{
                        vertical: 'bottom',
                        horizontal: 'center',
                      }}
                      transformOrigin={{
                        vertical: 'top',
                        horizontal: 'center',
                      }}
                      PaperProps={{
                        style: { width: '60%', borderRadius: "10px" },
                      }}
                    >
                      <SearchApps />
                    </Popover>
                  </div>
                )}
              </PopupState>
            </Box>
          </Box>
          <Typography
            variant="h6"
            noWrap
            component={Link}
            href="/"
            sx={{
              display: { xs: 'none', md: 'flex' },
              alignItems: 'center',
              gap: 1,
              fontWeight: 800,
              fontSize: '1.5rem',
              color: '#00A3FF',
              textDecoration: 'none',
              letterSpacing: '0.5px',
              fontFamily: "'Poppins', sans-serif",
              '&:hover': {
                color: '#0088cc',
                transform: 'scale(1.02)',
                transition: 'all 0.3s ease',
              },
              transition: 'all 0.3s ease',
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: -4,
                left: 0,
                width: '100%',
                height: '2px',
                backgroundColor: '#00A3FF',
                transform: 'scaleX(0)',
                transformOrigin: 'right',
                transition: 'transform 0.3s ease',
              },
              '&:hover::after': {
                transform: 'scaleX(1)',
                transformOrigin: 'left',
              }
            }}
          >
            <Box
              component="span"
              sx={{
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: 36,
                height: 36,
                backgroundColor: '#00A3FF',
                borderRadius: '8px',
                color: 'white',
                fontSize: '1.2rem',
                fontWeight: 'bold',
                mr: 0.5,
                boxShadow: '0 2px 8px rgba(0, 163, 255, 0.3)'
              }}
            >
              AS
            </Box>
            AppsStoreSpy
          </Typography>


          {/* {status === "authenticated" && (
            <Typography
              variant="subtitle2"
              sx={{
                fontWeight: "bold",
                marginLeft: "2rem",
                color: "#000",
                display: { xs: "none", md: "block" },
              }}
            >
              {session.user.email}
            </Typography>
          )} */}

          <Box sx={{ flexGrow: 1 }} />

          <Box sx={{ display: { xs: 'none', md: 'flex' }, alignItems: 'center' }}>
            <Button
              component={Link}
              href="/"
              sx={{
                color: pathname === '/' ? '#00A3FF' : '#000',
                fontWeight: 500,
                mr: 2,
                borderBottom: pathname === '/' ? '2px solid #00A3FF' : 'none'
              }}
            >
              Home
            </Button>
            {/* <Button
              component={Link}
              href="/optimization"
              sx={{
                color: pathname === '/optimization' ? '#00A3FF' : '#000',
                fontWeight: 500,
                mr: 2,
                borderBottom: pathname === '/optimization' ? '2px solid #00A3FF' : 'none'
              }}
            >
              Optimization
            </Button> */}
            <Button
              component={Link}
              href="/top-apps"
              sx={{
                color: pathname === '/top-apps' ? '#00A3FF' : '#000',
                fontWeight: 500,
                mr: 2,
                borderBottom: pathname === '/top-apps' ? '2px solid #00A3FF' : 'none'
              }}
            >
             Top Apps
            </Button>
             <Button
              component={Link}
              href="/top-games"
              sx={{
                color: pathname === '/top-games' ? '#00A3FF' : '#000',
                fontWeight: 500,
                mr: 2,
                borderBottom: pathname === '/top-games' ? '2px solid #00A3FF' : 'none'
              }}
            >
             Top Games
            </Button>
            {/* <Button
              component={Link}
              href="/top-charts"
              sx={{
                color: pathname === '/top-charts' ? '#00A3FF' : '#000',
                fontWeight: 500,
                mr: 2,
                borderBottom: pathname === '/top-charts' ? '2px solid #00A3FF' : 'none'
              }}
            >
              Top Charts
            </Button> */}
            {/* <Button
              component={Link}
              href="/pricing"
              sx={{
                color: pathname === '/pricing' ? '#00A3FF' : '#000',
                fontWeight: 500,
                mr: 2,
                borderBottom: pathname === '/pricing' ? '2px solid #00A3FF' : 'none'
              }}
            >
              Pricing
            </Button> */}
            <PopupState variant="popover" popupId="demo-popup-popover">
              {(popupState) => (
                <div>
                  <IconButton
                    {...bindTrigger(popupState)}
                    size="large"
                    aria-label="search"
                    color="inherit"
                    sx={{
                      ml: 2,
                      background: "#00A3FF",
                      "&:hover": {
                        backgroundColor: "#00A3FF",
                        boxShadow: "0 0 10px 3px rgba(0, 163, 255, 0.5)",
                        transform: "scale(1.1)",
                      },
                      transition: "all 0.2s ease-in-out",
                      color: "white"
                    }}
                  >
                    <SearchIcon />
                  </IconButton>
                  <Popover
                    {...bindPopover(popupState)}
                    anchorOrigin={{
                      vertical: 'bottom',
                      horizontal: 'center',
                    }}
                    transformOrigin={{
                      vertical: 'top',
                      horizontal: 'center',
                    }}
                    PaperProps={{
                      style: { width: '60%', borderRadius: "10px" },
                    }}
                  >
                    <SearchApps />
                  </Popover>
                </div>
              )}
            </PopupState>

            {status === "authenticated" ? (
              <IconButton
                size="large"
                edge="end"
                aria-label="account of current user"
                aria-controls={menuId}
                aria-haspopup="true"
                onClick={handleProfileMenuOpen}
                sx={{
                  ml: 2,
                  background: "#00A3FF",
                  "&:hover": {
                    backgroundColor: "#00A3FF",
                    boxShadow: "0 0 10px 3px rgba(0, 163, 255, 0.5)",
                    transform: "scale(1.1)",
                  },
                  transition: "all 0.2s ease-in-out",
                  color: "white"
                }}
              >
                {session.user?.image ? (
                  <Avatar
                    src={session.user.image}
                    sx={{
                      width: 32,
                      height: 32,
                      border: "2px solid white"
                    }}
                  />
                ) : (
                  <AccountCircle sx={{ color: "white" }} />
                )}
              </IconButton>
            ) : (
              <>
                <Button
                  variant="contained"
                  sx={{
                    backgroundColor: '#00A3FF',
                    '&:hover': { backgroundColor: '#0088cc' },
                    ml: 2
                  }}
                  onClick={() => router.push("/login")}
                >
                  Login
                </Button>
                <Button
                  variant="outlined"
                  sx={{
                    color: '#00A3FF',
                    borderColor: '#00A3FF',
                    '&:hover': { borderColor: '#0088cc' },
                    ml: 1
                  }}
                  onClick={() => router.push("/register")}
                >
                  Sign Up
                </Button>
              </>
            )}
          </Box>
        </Toolbar>
      </AppBar>


      <Box component="nav">
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: 280 },
          }}
        >
          {drawer}
        </Drawer>
      </Box>
      {renderMenu}
    </Box>
  );
};

export default Navbar;