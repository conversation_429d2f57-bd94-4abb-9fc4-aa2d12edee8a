import connectDB from "../../database/mongoose";
import { NextResponse } from "next/server";
import crypto from "crypto";
import User from "@/app/database/user";

export async function POST(req) {
  const { token } = await req.json();

  await connectDB();
  const hashedToken = crypto.createHash("sha256").update(token).digest("hex");

  const user = await User.findOne({
    resetToken: hashedToken,
    resetTokenExpiry: { $gt: Date.now() },
  });

  // console.log(user, "user");

  if (!user) {
    return new NextResponse("Invalid token or has expired", { status: 400 });
  }
  return new NextResponse(JSON.stringify(user), { status: 200 });
}
