import { AppRouterCacheProvider } from "@mui/material-nextjs/v13-appRouter";
import "bootstrap/dist/css/bootstrap.min.css";
import { Inter } from "next/font/google";
import BootStrapClient from "@/app/components/BootstrapClient";
import "../../globals.css";

const inter = Inter({ subsets: ["latin"] });
export const metadata = {
  title: "App Store Spy",
  description: "App analytics",
};
export default function RootLayout({children}) {
  return (
    <html lang="en">
        <body className={inter.className}>
          <AppRouterCacheProvider>
            {children}
            <BootStrapClient />
          </AppRouterCacheProvider>
        </body>
    </html>
  );
}
