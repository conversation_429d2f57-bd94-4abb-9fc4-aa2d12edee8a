import { NextResponse } from "next/server";
import gplay from "google-play-scraper";
import App from "@/app/database/appData";
import Developer from "@/app/database/developer";
import AppId from "@/app/database/appId";
import connectDB from "@/app/database/mongoose";
import { formatInstalls, formatRatingsAndReviews, formatReleasedDate, formatUpdateDate } from "@/app/utils/calculation";

export const GET = async (request) => {
  const url = new URL(request.url);
  const developerID = url.searchParams.get("developerID");
  const countryCode = url.searchParams.get("countryCode");

  if (!developerID) {
    return NextResponse.json({ error: "Dev ID is required" }, { status: 400 });
  }

  try {
    await connectDB();
    let developerData;

    developerData = await Developer.findOne({ developer: developerID });

    if (!developerData) {
        const gplayDeveloperData = await gplay.developer({ devId: developerID, country: countryCode || "US" });
        if (Array.isArray(gplayDeveloperData)) {
          const createdDeveloper = await Developer.findOneAndUpdate(
            { developer: developerID },
            {
              $setOnInsert: {
                developer: developerID,
                developerId: gplayDeveloperData[0].developerId || "",
                appIds: gplayDeveloperData.map((app) => app.appId),
                totalApps: gplayDeveloperData.length,
              },
            },
            { new: true, upsert: true }
          );
          for (const app of gplayDeveloperData) {
            await AppId.findOneAndUpdate(
              { appId: app.appId },
              { $setOnInsert: { appId: app.appId } },
              { upsert: true }
            );
          }
          developerData = createdDeveloper;
        } else {
          console.log(`Skipping ${developerID} due to unexpected data format.`);
          return NextResponse.json({ message: "Developer Information not found" }, { status: 404 });
        }
    }

    const developer = {
      info: {
        developerName: developerData.developer,
        totalApps: developerData.totalApps,
        totalInstalls: 0,
        totalReviews: 0,
        totalRatings: 0,
      },
      apps: [],
    };

    const appsByDeveloper = developerData.appIds;
    let latestUpdateTimestamp = 0;
    for (const appId of appsByDeveloper) {
      const app = await App.findOne({ appId });
      if (app) {
        const { appId, scoreText, icon, developer: appDeveloper, title, developerEmail, released, maxInstalls, reviews, ratings, updated, genre, developerAddress, developerWebsite } = app;
        developer.apps.push({ appId, scoreText, icon, appDeveloper, title, genre });

        latestUpdateTimestamp = Math.max(latestUpdateTimestamp, updated);
        developer.info.totalInstalls += maxInstalls;
        developer.info.totalReviews += reviews;
        developer.info.totalRatings += ratings;

        if (!developer.info.developerEmail) {
          Object.assign(developer.info, {
            developerEmail,
            developerAddress,
            developerWebsite,
            firstAppReleasedDate: formatReleasedDate(released)
          });
        }
      }
    }
    Object.assign(developer.info, {
      latestAppUpdated: formatUpdateDate(latestUpdateTimestamp),
      totalInstalls: formatInstalls(developer.info.totalInstalls),
      totalReviews: formatRatingsAndReviews(developer.info.totalReviews),
      totalRatings: formatRatingsAndReviews(developer.info.totalRatings)
    });
    return NextResponse.json({
      message: "Developer apps fetched and processed successfully",
      developer,
    }, { status: 200 });
  } catch (error) {
    return NextResponse.json(
      {
        message: "Error fetching developer apps",
        error,
      },
      { status: 500 }
    );
  }
};