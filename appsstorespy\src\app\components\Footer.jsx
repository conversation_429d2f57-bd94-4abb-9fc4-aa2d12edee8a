"use client";
import React from 'react';
import { Box, Container, Typography, Grid, Link, Divider } from '@mui/material';
import { Language, Apps, SmartToy, Analytics } from '@mui/icons-material';
import UniversalNavigation from './UniversalNavigation';

const Footer = () => {
  return (
    <Box
      component="footer"
      sx={{
        backgroundColor: '#f8fafc',
        borderTop: '1px solid #e2e8f0',
        py: 6,
        mt: 8
      }}
    >
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          {/* Company Info */}
          <Grid item xs={12} md={4}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Box
                sx={{
                  width: 36,
                  height: 36,
                  backgroundColor: '#00A3FF',
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontWeight: 'bold',
                  fontSize: '1.1rem',
                  mr: 2
                }}
              >
                AS
              </Box>
              <Typography
                variant="h6"
                sx={{
                  color: '#00A3FF',
                  fontWeight: 800,
                  fontFamily: "'Poppins', sans-serif"
                }}
              >
                AppsStoreSpy
              </Typography>
            </Box>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mb: 3, lineHeight: 1.6 }}
            >
              Comprehensive app analytics and market intelligence platform. 
              Track performance, analyze competitors, and optimize your app store presence.
            </Typography>
          </Grid>

          {/* Quick Links */}
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              Quick Links
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Link href="/" color="text.secondary" underline="hover">
                Home
              </Link>
              <Link href="/top-apps" color="text.secondary" underline="hover">
                Top Apps
              </Link>
              <Link href="/top-games" color="text.secondary" underline="hover">
                Top Games
              </Link>
            </Box>
          </Grid>

          {/* Legal */}
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              Legal
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Link href="/privacy-policy" color="text.secondary" underline="hover">
                Privacy Policy
              </Link>
              <Link href="/terms-conditions" color="text.secondary" underline="hover">
                Terms & Conditions
              </Link>
            </Box>
          </Grid>

          {/* Universal Navigation - Our Network */}
          <Grid item xs={12} md={4}>
            <UniversalNavigation variant="footer" />
          </Grid>
        </Grid>

        <Divider sx={{ my: 4 }} />

        {/* Bottom Section */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: 'center',
            gap: 2
          }}
        >
          <Typography variant="body2" color="text.secondary">
            © 2024 AppsStoreSpy. All rights reserved.
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2" color="text.secondary">
              Part of our network:
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Apps sx={{ fontSize: 16, color: 'text.secondary' }} />
              <SmartToy sx={{ fontSize: 16, color: 'text.secondary' }} />
              <Analytics sx={{ fontSize: 16, color: 'text.secondary' }} />
            </Box>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
