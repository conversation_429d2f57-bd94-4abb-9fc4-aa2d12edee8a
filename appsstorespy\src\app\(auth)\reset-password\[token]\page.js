"use client";
import React, { useEffect, useState } from 'react';
import { useRouter } from "next/navigation";
import axios from 'axios';
import {
  Box,
  Typography,
  Button,
  FormControl,
  InputLabel,
  OutlinedInput,
  InputAdornment,
  IconButton,
  FormHelperText,
  Link,
  Alert,
  CircularProgress
} from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';

const ResetPassword = ({ params }) => {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [pending, setPending] = useState(false);
  const [invalid, setInvalid] = useState('');
  const [user, setUser] = useState(null);
  const [isVerifying, setIsVerifying] = useState(true);
  const [userDetails, setUserDetails] = useState({
    password: '',
    confirmPassword: ''
  });
  const [errors, setErrors] = useState({
    password: '',
    confirmPassword: ''
  });

  const PASSWORD_REQUIREMENTS = [
    'Minimum 8 characters',
    'At least one uppercase letter',
    'At least one lowercase letter',
    'At least one number',
    'At least one special character (@$!%*?&)'
  ];

  const handleClickShowPassword = () => setShowPassword(!showPassword);
  const handleMouseDownPassword = (e) => e.preventDefault();

  const handleOnChange = (e) => {
    const { name, value } = e.target;
    setUserDetails(prev => ({ ...prev, [name]: value }));
    // Clear error when user types
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
    if (invalid) setInvalid('');
  };

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      password: '',
      confirmPassword: ''
    };

    if (!userDetails.password.trim()) {
      newErrors.password = 'Password is required';
      isValid = false;
    } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(userDetails.password)) {
      newErrors.password = 'Password does not meet requirements';
      isValid = false;
    }

    if (!userDetails.confirmPassword.trim()) {
      newErrors.confirmPassword = 'Please confirm your password';
      isValid = false;
    } else if (userDetails.password !== userDetails.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleFormSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      setPending(true);
      const response = await axios.post('/api/reset_password', {
        password: userDetails.password,
        email: user?.email,
      });

      if (response?.status === 200) {
        router.push("/login");
      } else {
        setInvalid(response?.data?.message || "Password reset failed");
      }
    } catch (error) {
      setInvalid(error.response?.data?.message || "Something went wrong. Please try again.");
    } finally {
      setPending(false);
    }
  };

  useEffect(() => {
    const verifyToken = async () => {
      try {
        const response = await axios.post('/api/verify-token', {
          token: params.token,
        });

        if (response?.status === 200) {
          setUser(response.data);
        } else {
          setInvalid(response?.data?.message || "Invalid or expired token");
          setPending(true);
        }
      } catch (error) {
        setInvalid(error.response?.data?.message || "Invalid or expired token");
        setPending(true);
      } finally {
        setIsVerifying(false);
      }
    };

    if (params.token) verifyToken();
  }, [params.token]);

  if (invalid) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '90vh',
        padding: 2
      }}>
        <Box sx={{
          maxWidth: '500px',
          textAlign: 'center'
        }}>
          <Alert
            severity="error"
            sx={{ mb: 2 }}
          >
            {invalid}
          </Alert>
          <Link
            href="/forget-password"
            sx={{
              color: 'primary.main',
              fontWeight: 500,
              textDecoration: 'underline',
              '&:hover': {
                textDecoration: 'none'
              }
            }}
          >
            Request new reset link
          </Link>
        </Box>
      </Box>
    );
  }

  return (
    <>
      {isVerifying ? (
        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '90vh'
        }}>
          <CircularProgress size={60} />
        </Box>
      ) : (
        <Box sx={{
          display: "flex",
          minHeight: "100vh",
          backgroundColor: "#f8fafc"
        }}>
          {/* Left side - Illustration */}
          <Box sx={{
            display: { xs: "none", md: "flex" },
            width: "50%",
            backgroundColor: "#00A3FF",
            justifyContent: "center",
            alignItems: "center",
            padding: "2rem"
          }}>
            <Box sx={{
              maxWidth: "500px",
              textAlign: "center",
              color: "white"
            }}>
              <Typography variant="h3" sx={{
                fontWeight: 800,
                mb: 3,
                fontSize: { md: "1.5rem", lg: "2rem" }
              }}>
                Reset Your Password
              </Typography>
              <Typography variant="body1" sx={{
                fontSize: "1.1rem",
                lineHeight: 1.6,
                mb: 4
              }}>
                Secure your AppsStoreSpy account with a new password
              </Typography>
              <Box sx={{
                mt: 4,
                '& img': {
                  borderRadius: '12px',
                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
                }
              }}>
                <img
                  src="https://images.unsplash.com/photo-**********-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                  alt="Password Security"
                  style={{ width: "100%", maxWidth: "400px" }}
                />
              </Box>
            </Box>
          </Box>

          {/* Right side - Form */}
          <Box sx={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            width: { xs: "100%", md: "50%" },
            padding: { xs: "2rem", md: "4rem" }
          }}>
            <Box sx={{
              mb: 4,
              textAlign: "center",
              '& h4': {
                fontWeight: 700,
                color: "#101828",
                mb: 1
              },
              '& p': {
                color: "#667085"
              }
            }}>
              <Typography variant="h4">
                Create New Password
              </Typography>
              <Typography variant="body1">
                Your new password must be different from previous ones
              </Typography>
            </Box>

            <Box sx={{
              width: "100%",
              maxWidth: "500px",
              margin: "0 auto",
              backgroundColor: "white",
              borderRadius: "12px",
              boxShadow: "0 4px 12px rgba(0, 0, 0, 0.08)",
              padding: { xs: "1.5rem", sm: "2rem" }
            }}>
              {invalid && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {invalid}
                </Alert>
              )}

              <form onSubmit={handleFormSubmit}>
                <Box sx={{ mb: 2 }}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel htmlFor="password">New Password</InputLabel>
                    <OutlinedInput
                      id="password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      value={userDetails.password}
                      onChange={handleOnChange}
                      error={Boolean(errors.password)}
                      endAdornment={
                        <InputAdornment position="end">
                          <IconButton
                            aria-label="toggle password visibility"
                            onClick={handleClickShowPassword}
                            onMouseDown={handleMouseDownPassword}
                            edge="end"
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      }
                      label="New Password"
                    />
                    {errors.password && (
                      <FormHelperText error>{errors.password}</FormHelperText>
                    )}
                  </FormControl>

                  <Box sx={{
                    mt: 1,
                    pl: 1.5,
                    '& ul': {
                      pl: 2,
                      m: 0,
                      '& li': {
                        fontSize: '0.75rem',
                        color: '#667085',
                        lineHeight: 1.5
                      }
                    }
                  }}>
                    <Typography variant="caption">Password requirements:</Typography>
                    <ul>
                      {PASSWORD_REQUIREMENTS.map((req, index) => (
                        <li key={index}>{req}</li>
                      ))}
                    </ul>
                  </Box>
                </Box>

                <Box sx={{ mb: 3 }}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel htmlFor="confirm-password">Confirm Password</InputLabel>
                    <OutlinedInput
                      id="confirm-password"
                      name="confirmPassword"
                      type={showPassword ? "text" : "password"}
                      value={userDetails.confirmPassword}
                      onChange={handleOnChange}
                      error={Boolean(errors.confirmPassword)}
                      endAdornment={
                        <InputAdornment position="end">
                          <IconButton
                            aria-label="toggle password visibility"
                            onClick={handleClickShowPassword}
                            onMouseDown={handleMouseDownPassword}
                            edge="end"
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      }
                      label="Confirm Password"
                    />
                    {errors.confirmPassword && (
                      <FormHelperText error>{errors.confirmPassword}</FormHelperText>
                    )}
                  </FormControl>
                </Box>

                <Button
                  fullWidth
                  variant="contained"
                  size="large"
                  type="submit"
                  disabled={pending}
                  sx={{
                    backgroundColor: "#00A3FF",
                    py: 1.5,
                    borderRadius: "8px",
                    textTransform: "none",
                    fontSize: "1rem",
                    fontWeight: 600,
                    "&:hover": {
                      backgroundColor: "#0088cc"
                    },
                    '&.Mui-disabled': {
                      backgroundColor: '#e0e0e0'
                    }
                  }}
                >
                  {pending ? (
                    <>
                      <CircularProgress size={24} sx={{ color: "white", mr: 1 }} />
                      Resetting...
                    </>
                  ) : (
                    "Reset Password"
                  )}
                </Button>
              </form>

              <Box sx={{
                mt: 3,
                textAlign: "center",
                '& a': {
                  color: "#00A3FF",
                  fontWeight: 600,
                  textDecoration: "none",
                  "&:hover": {
                    textDecoration: "underline"
                  }
                }
              }}>
                <Typography variant="body2" sx={{ color: "#667085" }}>
                  Remember your password?{" "}
                  <Link href="/login">
                    Login here
                  </Link>
                </Typography>
              </Box>
            </Box>

            <Box sx={{
              mt: 4,
              textAlign: "center",
              '& p': {
                color: "#98A2B3",
                fontSize: '0.875rem'
              }
            }}>
              <Typography variant="body2">
                © {new Date().getFullYear()} AppsStoreSpy. All rights reserved.
              </Typography>
            </Box>
          </Box>
        </Box>

      )}
    </>

  );
};

export default ResetPassword;