"use client";
import React from 'react';
import Link from 'next/link';
import { 
  generateLatestAPKURL,
  generateOlderVersionsAPKURL,
  generateAnchorText, 
  trackCrossLinkClick,
  isCrossLinkingEnabled 
} from '../utils/crossLinking';

/**
 * Dual APK Links Component for AI Tools pages
 * Shows both "Get Latest Version" and "Get Older Versions" buttons
 */
const DualAPKLinks = ({ 
  toolDetails, 
  variant = 'horizontal',
  size = 'medium',
  className = '',
  showIcons = true 
}) => {
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const latestURL = generateLatestAPKURL(toolDetails);
  const olderVersionsURL = generateOlderVersionsAPKURL(toolDetails);

  // Don't render if no URLs could be generated
  if (!latestURL && !olderVersionsURL) {
    return null;
  }

  const buttons = [];

  if (latestURL) {
    buttons.push({
      type: 'latest-apk',
      url: latestURL,
      text: 'Get Latest Version',
      icon: '📱',
      color: 'bg-green-600 hover:bg-green-700',
      description: `Download the latest version of ${toolDetails?.title || 'related apps'}`
    });
  }

  if (olderVersionsURL) {
    buttons.push({
      type: 'older-apk',
      url: olderVersionsURL,
      text: 'Get Older Versions',
      icon: '📦',
      color: 'bg-purple-600 hover:bg-purple-700',
      description: `Download older versions of ${toolDetails?.title || 'related apps'}`
    });
  }

  // Style variants
  const sizes = {
    small: 'px-3 py-2 text-sm',
    medium: 'px-6 py-3 text-base',
    large: 'px-8 py-4 text-lg'
  };

  const containerClasses = variant === 'vertical' 
    ? 'flex flex-col gap-3' 
    : 'flex flex-col sm:flex-row gap-3 justify-center items-center';

  const handleClick = (button) => {
    trackCrossLinkClick('ai-tools', button.type, toolDetails);
  };

  return (
    <div className={`${containerClasses} ${className}`}>
      {buttons.map((button, index) => (
        <Link
          key={index}
          href={button.url}
          onClick={() => handleClick(button)}
          target="_blank"
          rel="noopener noreferrer"
          className={`
            inline-flex items-center justify-center gap-2 
            font-medium rounded-lg transition-all duration-200 
            focus:outline-none focus:ring-2 focus:ring-offset-2
            transform hover:scale-105 active:scale-95 text-white
            ${button.color} 
            ${sizes[size]}
          `}
          title={button.description}
        >
          {showIcons && <span className="text-lg">{button.icon}</span>}
          <span>{button.text}</span>
          <svg 
            className="w-4 h-4" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" 
            />
          </svg>
        </Link>
      ))}
    </div>
  );
};

/**
 * Compact Dual APK Links for sidebars
 */
export const CompactDualAPKLinks = ({ toolDetails, className = '' }) => {
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const latestURL = generateLatestAPKURL(toolDetails);
  const olderVersionsURL = generateOlderVersionsAPKURL(toolDetails);

  const links = [];
  
  if (latestURL) {
    links.push({ url: latestURL, text: 'Latest APK', type: 'latest-apk' });
  }
  if (olderVersionsURL) {
    links.push({ url: olderVersionsURL, text: 'Older APKs', type: 'older-apk' });
  }

  if (links.length === 0) {
    return null;
  }

  const handleClick = (link) => {
    trackCrossLinkClick('ai-tools', link.type, toolDetails);
  };

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {links.map((link, index) => (
        <Link
          key={index}
          href={link.url}
          onClick={() => handleClick(link)}
          target="_blank"
          rel="noopener noreferrer"
          className="
            inline-flex items-center gap-1 text-sm text-green-600 hover:text-green-800 
            hover:underline transition-colors duration-200 px-2 py-1 rounded
            bg-green-50 hover:bg-green-100
          "
        >
          {link.text}
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
          </svg>
        </Link>
      ))}
    </div>
  );
};

/**
 * Banner Dual APK Links for prominent placement
 */
export const BannerDualAPKLinks = ({ toolDetails, className = '' }) => {
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const latestURL = generateLatestAPKURL(toolDetails);
  const olderVersionsURL = generateOlderVersionsAPKURL(toolDetails);
  
  if (!latestURL && !olderVersionsURL) {
    return null;
  }

  return (
    <div className={`bg-gradient-to-r from-green-50 to-purple-50 border border-green-200 rounded-lg p-4 ${className}`}>
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            Get Mobile Access
          </h3>
          <p className="text-gray-600 text-sm">
            Download {toolDetails?.title || 'related apps'} and similar mobile apps for Android
          </p>
        </div>
        <DualAPKLinks 
          toolDetails={toolDetails} 
          variant="horizontal" 
          size="medium"
        />
      </div>
    </div>
  );
};

/**
 * Card version with detailed information
 */
export const DualAPKCard = ({ toolDetails, className = '' }) => {
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const latestURL = generateLatestAPKURL(toolDetails);
  const olderVersionsURL = generateOlderVersionsAPKURL(toolDetails);
  
  if (!latestURL && !olderVersionsURL) {
    return null;
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow ${className}`}>
      <div className="flex items-start gap-4">
        <div className="flex-shrink-0">
          <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Mobile Apps Available
          </h3>
          <p className="text-gray-600 text-sm mb-4">
            Access {toolDetails?.title || 'this tool'} and similar functionality on your Android device. 
            Choose between the latest version or explore older releases.
          </p>
          <DualAPKLinks 
            toolDetails={toolDetails} 
            variant="horizontal" 
            size="small"
          />
        </div>
      </div>
    </div>
  );
};

export default DualAPKLinks;
