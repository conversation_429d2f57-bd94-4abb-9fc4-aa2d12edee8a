"use client";
import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON>Field, Box, Typography, CircularProgress,Alert  } from "@mui/material";
import FormHelperText from "@mui/material/FormHelperText";
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import InputAdornment from "@mui/material/InputAdornment";
import IconButton from "@mui/material/IconButton";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import OutlinedInput from "@mui/material/OutlinedInput";
import Link from "next/link";
import { useRouter } from "next/navigation";
import axios from "axios";

const RegisterPage = () => {
  const router = useRouter();
  const [userDetails, setUserDetails] = useState({
    email: "",
    password: "",
    confirmPassword: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loginError, setLoginError] = useState({
    emailError: "",
    passwordError: "",
    confirmPasswordError: "",
  });
  const [pending, setPending] = useState(false);
  const [invalid, setInvalid] = useState("");
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

  const passRegex =
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@.#$!%*?&])[A-Za-z\d@.#$!%*?&]{8,15}$/;

  const handleClickShowPassword = () => setShowPassword((show) => !show);

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const isPasswordMismatch = userDetails.password.trim() !== userDetails.confirmPassword.trim();
    const isEmailInvalid = !userDetails.email.trim() || !emailRegex.test(userDetails.email);
    const isPasswordInvalid = !userDetails.password.trim() || !passRegex.test(userDetails.password);
    const isConfirmPasswordEmpty = !userDetails.confirmPassword.trim();

    if (isPasswordMismatch || isEmailInvalid || isPasswordInvalid || isConfirmPasswordEmpty) {
      setLoginError({
        confirmPasswordError: isPasswordMismatch ? "Passwords do not match" : "",
        emailError: isEmailInvalid ? "Please enter your email" : "",
        passwordError: isPasswordInvalid ? "Please enter your password" : "",
        confirmPasswordError: isConfirmPasswordEmpty ? "Please enter your confirm password" : "",
      });
      return;
    }
    try {
      setPending(true);
      const response = await axios.post(`/api/register`, {
        email: userDetails.email,
        password: userDetails.confirmPassword,
      });

      if (response && response.status === 200) {
        setPending(false);
        setUserDetails({
          email: "",
          password: "",
          confirmPassword: "",
        });
        router.push("/login");
      } else {
        setInvalid(response.data.message);
        setPending(false);
      }
    } catch (error) {
      setPending(false);
      setInvalid("Something went wrong");
      console.error("Error:", error);
    }
  };
  const handleOnChange = (e) => {
    const { name, value } = e.target;
    setUserDetails((prevState) => ({
      ...prevState,
      [name]: value,
    }));

    switch (name) {
      case "email":
        {
          if (!value.trim()) {
            setLoginError((prevError) => ({
              ...prevError,
              emailError: "Email is required*",
            }));
          } else if (!emailRegex.test(value)) {
            setLoginError((prevError) => ({
              ...prevError,
              emailError: "Invalid email format",
            }));
          } else {
            setLoginError((prevError) => ({
              ...prevError,
              emailError: "",
            }));
          }
        }

        break;
      case "password":
        {
          if (!value.trim()) {
            setLoginError((prevError) => ({
              ...prevError,
              passwordError: "Password is required*",
            }));
          } else if (!passRegex.test(value)) {
            setLoginError((prevError) => ({
              ...prevError,
              passwordError:
                "Password must be 8-15 characters long and contain at least one lowercase letter, one uppercase letter, one number, and one special character",
            }));
          } else {
            setLoginError((prevError) => ({
              ...prevError,
              passwordError: "",
            }));
          }
        }

        break;
      case "confirmPassword":
        {
          if (!value.trim()) {
            setLoginError((prevError) => ({
              ...prevError,
              confirmPasswordError: "confirmPassword is required*",
            }));
          } else if (!(userDetails.password === value)) {
            setLoginError((prevError) => ({
              ...prevError,
              confirmPasswordError: "Passwords do not match",
            }));
          } else {
            setLoginError((prevError) => ({
              ...prevError,
              confirmPasswordError: "",
            }));
          }
        }

        break;
      default:
        break;
    }
    setInvalid("");
  };

  return (
    <Box
      sx={{
        display: "flex",
        minHeight: "100vh",
        backgroundColor: "#f8fafc"
      }}
    >
      <Box
        sx={{
          display: { xs: "none", md: "flex" },
          width: "50%",
          backgroundColor: "#00A3FF",
          justifyContent: "center",
          alignItems: "center",
          padding: "2rem"
        }}
      >
        <Box sx={{ maxWidth: "500px", textAlign: "center" }}>
          <Typography
            variant="h3"
            sx={{
              fontWeight: 800,
              color: "white",
              mb: 3,
              fontSize: "2.5rem"
            }}
          >
            Welcome to AppsStoreSpy
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: "rgba(255,255,255,0.9)",
              fontSize: "1.1rem",
              lineHeight: 1.6
            }}
          >
            Track, analyze and optimize your play store performance with our powerful analytics platform.
          </Typography>
          <Box sx={{ mt: 4 }}>
            <img
              src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
              alt="App Analytics"
              style={{ width: "100%", borderRadius: "8px" }}
            />
          </Box>

        </Box>
      </Box>

      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          width: { xs: "100%", md: "50%" },
          padding: { xs: "2rem", md: "4rem" }
        }}
      >
        <Box sx={{ mb: 4, textAlign: "center" }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              color: "#101828",
              mb: 1
            }}
          >
            Create an account
          </Typography>
          <Typography variant="body1" sx={{ color: "#667085" }}>
            Get started with your free account today
          </Typography>
        </Box>

        <Box
          sx={{
            width: "100%",
            maxWidth: "500px",
            margin: "0 auto",
            backgroundColor: "white",
            borderRadius: "12px",
            boxShadow: "0 4px 12px rgba(0, 0, 0, 0.08)",
            padding: { xs: "1.5rem", sm: "2rem" }
          }}
        >
          {invalid && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {invalid}
            </Alert>
          )}

          <form onSubmit={handleSubmit}>
            <Box sx={{ mb: 2 }}>
              <TextField
                fullWidth
                name="email"
                label="Email"
                variant="outlined"
                onChange={handleOnChange}
                error={Boolean(loginError.emailError)}
                helperText={loginError.emailError}
                sx={{ mb: 1 }}
              />
            </Box>

            <Box sx={{ mb: 2 }}>
              <FormControl fullWidth variant="outlined">
                <InputLabel htmlFor="password">Password</InputLabel>
                <OutlinedInput
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  onChange={handleOnChange}
                  error={Boolean(loginError.passwordError)}
                  endAdornment={
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={handleClickShowPassword}
                        onMouseDown={handleMouseDownPassword}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  }
                  label="Password"
                />
                {loginError.passwordError && (
                  <FormHelperText error>
                    {loginError.passwordError}
                  </FormHelperText>
                )}
              </FormControl>
            </Box>

            <Box sx={{ mb: 3 }}>
              <FormControl fullWidth variant="outlined">
                <InputLabel htmlFor="confirm-password">Confirm Password</InputLabel>
                <OutlinedInput
                  id="confirm-password"
                  name="confirmPassword"
                  type={showPassword ? "text" : "password"}
                  onChange={handleOnChange}
                  error={Boolean(loginError.confirmPasswordError)}
                  endAdornment={
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={handleClickShowPassword}
                        onMouseDown={handleMouseDownPassword}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  }
                  label="Confirm Password"
                />
                {loginError.confirmPasswordError && (
                  <FormHelperText error>
                    {loginError.confirmPasswordError}
                  </FormHelperText>
                )}
              </FormControl>
            </Box>

            <Button
              fullWidth
              variant="contained"
              size="large"
              type="submit"
              disabled={pending}
              sx={{
                backgroundColor: "#00A3FF",
                py: 1.5,
                borderRadius: "8px",
                textTransform: "none",
                fontSize: "1rem",
                fontWeight: 600,
                "&:hover": {
                  backgroundColor: "#0088cc"
                }
              }}
            >
              {pending ? (
                <>
                  <CircularProgress size={24} sx={{ color: "white", mr: 1 }} />
                  Creating account...
                </>
              ) : (
                "Create account"
              )}
            </Button>
          </form>

          <Box sx={{ mt: 3, textAlign: "center" }}>
            <Typography variant="body2" sx={{ color: "#667085" }}>
              Already have an account?{" "}
              <Link
                href="/login"
                sx={{
                  color: "#00A3FF",
                  fontWeight: 600,
                  textDecoration: "none",
                  "&:hover": { textDecoration: "underline" }
                }}
              >
                Log in
              </Link>
            </Typography>
          </Box>
        </Box>

        <Box sx={{ mt: 4, textAlign: "center" }}>
          <Typography variant="body2" sx={{ color: "#98A2B3" }}>
            © {new Date().getFullYear()} AppsStoreSpy. All rights reserved.
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default RegisterPage;
