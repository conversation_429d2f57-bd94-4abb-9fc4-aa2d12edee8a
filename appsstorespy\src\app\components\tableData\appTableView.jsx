"use client";
import React, { useState } from "react";
import Link from 'next/link';
import {
  Table,
  TableHead,
  Select as MuiSelect,
  TableRow,
  MenuItem,
  TableContainer,
  TableCell,
  TableBody,
  FormControl,
  Typography,
  TableFooter,
  Paper,
  Button,
  Box,
  Grid,
  InputLabel,
  TablePagination,
} from "@mui/material";
import Tooltip from '@mui/material/Tooltip';
import { connect } from "react-redux";
import ArrowDropDownRoundedIcon from '@mui/icons-material/ArrowDropDownRounded';
import ArrowDropUpRoundedIcon from '@mui/icons-material/ArrowDropUpRounded';
import IconButton from '@mui/material/IconButton';
import FirstPageIcon from '@mui/icons-material/FirstPage';
import KeyboardArrowLeft from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRight from '@mui/icons-material/KeyboardArrowRight';
import LastPageIcon from '@mui/icons-material/LastPage';
import StarIcon from "@mui/icons-material/Star";
import LockIcon from "@mui/icons-material/Lock";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import GridViewIcon from '@mui/icons-material/GridView';
import TableViewIcon from '@mui/icons-material/TableView';
import { useDispatch } from 'react-redux';
import {
  formatInstalls,
  formatRatingsAndReviews,
  formatReleasedDate, 
  formatUpdateDate,
} from "../../utils/calculation";
import {categories} from "../../utils/constants";
import { useTheme } from '@mui/material/styles';
import GridViewApp from "./appGridView";
import { resetSearchApps ,fetchTopAppDetails, updateCategory, resetCategory } from "@/app/redux/slice/topAppSlice";
import { countries } from "@/app/utils/countries";

const ITEM_HEIGHT = 78;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 200,
    },
  },
};

const AppCard = (props) => {
  const dispatch = useDispatch();
  const visibleColumns = [
    props.topAppsByCountry ? "Ranks" : "Sr No",
    "Name",
    "Downloads",
    !props.topAppsByCountry ? "Type" : null,
    "Category",
    "Ratings",
    "Reviews",
    "Released Date",
    "Last Updated",
    "Ads",
  ].filter(Boolean);;
  const [viewMode, setViewMode] = useState("table");

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
    const handleChange = (e) => {
      const selectedCategory = e.target.value;
      dispatch(updateCategory(selectedCategory));

      dispatch(fetchTopAppDetails({ countryCode: props.countryCode, collection: props.collection, category: selectedCategory }));
    };

  function TablePaginationActions(props) {
    const theme = useTheme();
    const { count, page, rowsPerPage, onPageChange } = props;
  
    const handleFirstPageButtonClick = (event) => {
      onPageChange(event, 0);
    };
  
    const handleBackButtonClick = (event) => {
      onPageChange(event, page - 1);
    };
  
    const handleNextButtonClick = (event) => {
      onPageChange(event, page + 1);
    };
  
    const handleLastPageButtonClick = (event) => {
      onPageChange(event, Math.max(0, Math.ceil(count / rowsPerPage) - 1));
    };
  
    return (
      <Box sx={{ flexShrink: 0, ml: 2.5 }} >
        <IconButton
          onClick={handleFirstPageButtonClick}
          disabled={page === 0}
          aria-label="first page"
        >
          {theme.direction === 'rtl' ? <LastPageIcon /> : <FirstPageIcon />}
        </IconButton>
        <IconButton
          onClick={handleBackButtonClick}
          disabled={page === 0}
          aria-label="previous page"
        >
          {theme.direction === 'rtl' ? <KeyboardArrowRight /> : <KeyboardArrowLeft />}
        </IconButton>
        <IconButton
          onClick={handleNextButtonClick}
          disabled={page >= Math.ceil(count / rowsPerPage) - 1}
          aria-label="next page"
        >
          {theme.direction === 'rtl' ? <KeyboardArrowLeft /> : <KeyboardArrowRight />}
        </IconButton>
        <IconButton
          onClick={handleLastPageButtonClick}
          disabled={page >= Math.ceil(count / rowsPerPage) - 1}
          aria-label="last page"
        >
          {theme.direction === 'rtl' ? <FirstPageIcon /> : <LastPageIcon />}
        </IconButton>
      </Box>
    );
  }

  const getCountryName = (countryCode) => {
    const country = countries.find((c) => c.code === countryCode);
    return country ? country.label : "";
  };
  const formatCollectionName = (collection) => {
    if (!collection) {
      return (
        <>
        <Box sx={{
          display:"flex",
          justifyContent:"space-between",
          width:'30%',
        }}>
            <Box sx={{ borderRadius: '5px', padding: '0.5rem', background: '#eff9ff' }}>
                <img src="https://cdn-icons-png.flaticon.com/128/300/300218.png" loading="lazy" alt="PlayStore" title="PlayStore" width="20" height="20" />
              </Box>
          <Box
          sx={{
            padding:"8px",
            borderRadius:"2rem",
            backgroundColor:"#eff9ff"
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', marginLeft: '5px' }}>
          &nbsp;
          <img
            loading="lazy"
            width="25"
            srcSet={`https://flagcdn.com/w40/${props.searchParams.countryCode.toLowerCase()}.png 2x`}
            src={`https://flagcdn.com/w20/${props.searchParams.countryCode.toLowerCase()}.png`}
            alt={props.searchParams.countryCode}
            title={props.searchParams.countryCode}
          />
          &nbsp;
          <Typography variant="h6" fontWeight={600} fontSize="1rem">
          {getCountryName(props.searchParams.countryCode)}
          </Typography>
        </Box>
          </Box>
          <Box
          sx={{
            padding:"8px",
            borderRadius:"2rem",
            backgroundColor:"#eff9ff"
          }}>
             <Typography variant="h6" fontWeight={600} fontSize="1rem"> {props.searchParams.price}</Typography>
          </Box>
          <Box
          sx={{
            padding:"8px",
            borderRadius:"2rem",
            backgroundColor:"#eff9ff"
          }}>
             <Typography variant="h6" fontWeight={600} fontSize="1rem"> {props.searchParams.searchTerm}</Typography>
          </Box>

        </Box>
        <Typography variant="h6" fontWeight={600} fontSize="1rem">
          Search apps founds
        </Typography>
        </>
      );
    }

    const formattedCollection = collection
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');

    return (
      <Typography variant="h6" fontWeight={600} fontSize="1rem" display="flex">
        {`${formattedCollection} Apps`}
        <Box sx={{ display: 'flex', alignItems: 'center', marginLeft: '5px' }}>
          &nbsp;
          <img
            loading="lazy"
            width="25"
            srcSet={`https://flagcdn.com/w40/${props.countryCode.toLowerCase()}.png 2x`}
            src={`https://flagcdn.com/w20/${props.countryCode.toLowerCase()}.png`}
            alt={props.country}
            title={props.countryCode}
          />
          &nbsp;
          <Typography paragraph sx={{ color: "#716666", marginBottom: 0 }}>
            {props.country}
          </Typography>
        </Box>
      </Typography>
    );
  };
  return (
    <Box>
      <Box
        sx={{
          marginBottom: "0.5rem",
          display: "flex",
          padding: "5px",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
       <Button
          variant="outlined"
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: "#00A3FF",
            "&:hover": {
              backgroundColor: "#00A3FF",
              color: "#fff",
            },
            textTransform: "none",
            letterSpacing: "2px",
            borderRadius: "12px"
          }}
          onClick={() => {
            if( props.topAppsByCountry){
              dispatch(fetchTopAppDetails({ countryCode: props.countryCode, collection: props.collection}))
              dispatch(resetCategory());
              props.setViewAllApps(false);
            }else{
             dispatch(resetSearchApps());
            }
          }}
        >
          <ArrowBackIcon />
        </Button>
        
        {formatCollectionName(props.collection)}
        {props.topAppsByCountry ? <Box width="30%"> <Grid item xs={12} sm={6} md={6}>
                <FormControl fullWidth>
                  <InputLabel id="category">Category</InputLabel>
                  <MuiSelect
                    labelId="category"
                    value={props.category}
                    label="category"
                    onChange={handleChange}
                    MenuProps={MenuProps}
                  >
                     {Object.keys(categories).map((key) => (
                    <MenuItem key={key} value={categories[key]}>
                      {categories[key]}
                    </MenuItem>
                  ))}
                  </MuiSelect>
                </FormControl>
              </Grid></Box>: null }
        <Box
          sx={{
            display: "flex",
            justifyContent: "end",
            alignItems: "center",
            width: "10%"
          }}
        >
          {viewMode === "table" ? (
            <Button
              sx={{
                background: "#00A3FF", "&:hover": {
                  backgroundColor: "#00A3FF",
                  color: "#fff",
                }, textTransform: "none", letterSpacing: "2px", borderRadius: "12px"
              }}
              variant="contained"
              onClick={() => setViewMode("grid")}
            >
              <TableViewIcon />&nbsp;Table
            </Button>
          ) : (
            <Button
              sx={{
                background: "#00A3FF", "&:hover": {
                  backgroundColor: "#00A3FF",
                  color: "#fff",
                }, textTransform: "none", letterSpacing: "2px", borderRadius: "12px"
              }}
              variant="contained"
              onClick={() => setViewMode("table")}
            >
              <GridViewIcon />&nbsp;Grid
            </Button>
          )}
        </Box>
      </Box>

      {viewMode === "table" ? <TableContainer component={Paper} sx={{borderRadius:"1rem", boxShadow: "rgba(100, 100, 111, 0.2) 0px 7px 29px 0px"}}>
        <Table aria-label="simple table">
          <TableHead>
            <TableRow>
              {visibleColumns.map((column) => (
                <TableCell
                  key={column}
                  sx={{
                    width: column === "Name" ? "350px" : "auto",
                    fontWeight: "bold",
                    textAlign: "center",
                    textTransform: "capitalize",
                  }}
                >
                  {column}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {props.apps && props.apps.length > 0 ? props.apps
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((appData, index) => {
                const tooltipText = `
                ${props.topAppsByCountry && appData.ranking && appData.ranking.rank === '+' ? `Increased by  ${appData.ranking.position}` : ''} 
                ${props.topAppsByCountry && appData.ranking && appData.ranking.rank === '-' ? `Decreased by  ${appData.ranking.position}` : ''}
                ${props.topAppsByCountry && appData.ranking && appData.ranking.position === '=' ? 'Same position' : ''}
                ${props.topAppsByCountry && appData.ranking && appData.ranking.position === 'new' ? 'New app' : ''}
              `;
               return <TableRow
                  key={appData.appId}
                  sx={{
                    borderBottom: 'none',
                    color:"#716c6c",
                    backgroundColor: index % 2 === 0 ? '#eff9ff' : '#fff',
                    "&:hover": {
                      backgroundColor: '#daf1ff',
                    },
                  }}
                >
                  <TableCell sx={{textAlign:"center",borderBottom:"none"}}>{props.topAppsByCountry ? appData.position : index + 1}</TableCell>
                  <TableCell sx={{ padding: "8px",borderBottom:"none"}}>
                    <Box display="flex">
                      <Box>
                        <img
                          src={appData.icon}
                          alt={appData.title}
                          style={{
                            width: "60px",
                            height: "60px",
                            margin: "0.5rem",
                            borderRadius: "16px",
                            aspectRatio: "1/1",
                          }}
                        />
                      </Box>
                      <Box sx={{
                        display:"flex",
                        justifyContent:"space-between",
                        width:"100%"
                      }}>

                      <Box>
                        <Typography
                          variant="h3"
                          sx={{
                            fontSize: "1.2rem",
                          }}
                        >
                          <Link href={`/app-details/${appData.appId}`}>
                           {appData.title}
                          </Link>
                        </Typography>
                        <Typography variant="subtitle1" sx={{
                          color: "#716c6c",
                        }}>
                          <Link href={`/developer-apps/${appData.developer}`}>
                          {appData.developer}
                          </Link>
                        </Typography>
                        <Typography sx={{color:"#716c6c"}}>
                          {appData.scoreText}&nbsp;
                          <StarIcon sx={{ color: "#FFDF64" }} />
                        </Typography>
                      </Box>
                      {props.topAppsByCountry && appData.ranking ? <Box sx={{
                      display: "flex",
                      alignItems: "center",
                      cursor: "pointer",
                      color: appData.ranking.rank === "+" ? "#80af3f" : appData.ranking.rank === "-" ? "#ef4723" : "#00A3FF",
                    }}>
                      <Tooltip title={tooltipText} arrow placement="top">
                        <Typography variant="h3" sx={{
                          fontSize: "1rem",
                          fontWeight: 600,
                          display: "flex",
                          alignItems: "center",
                          fontFamily: "system-ui"
                        }}>
                          {appData.ranking.rank === "+" && <ArrowDropUpRoundedIcon />}
                          {appData.ranking.rank === "-" && <ArrowDropDownRoundedIcon />}
                          {appData.ranking.position}
                        </Typography>
                      </Tooltip>
                    </Box> : null }
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell sx={{textAlign:"center",borderBottom:"none",color:"#716c6c"}}>{formatInstalls(appData.maxInstalls)}</TableCell>
                  {!props.topAppsByCountry && <TableCell sx={{textAlign:"center",borderBottom:"none",color:"#716c6c"}}>{appData.free ? "Free" : "Paid"}</TableCell>}
                  <TableCell sx={{textAlign:"center",borderBottom:"none",color:"#716c6c"}}>{appData.genre}</TableCell>
                  <TableCell sx={{textAlign:"center",borderBottom:"none",color:"#716c6c"}}>{formatRatingsAndReviews(appData.ratings)}</TableCell>
                  <TableCell sx={{textAlign:"center",borderBottom:"none",color:"#716c6c"}}>{formatRatingsAndReviews(appData.reviews)}</TableCell>
                  <TableCell sx={{textAlign:"center",borderBottom:"none",color:"#716c6c"}}>
                    {appData.released ? formatReleasedDate(appData.released, true) : "-"}
                  </TableCell>
                  <TableCell sx={{textAlign:"center",borderBottom:"none",color:"#716c6c"}}>
                    {appData.updated ? formatUpdateDate(appData.updated, true) : "-"}
                  </TableCell>
                  <TableCell sx={{textAlign:"center",borderBottom:"none",color:"#716c6c"  }}>
                    {appData.adSupported !== undefined ? (
                      appData.adSupported ? "Yes" : "No"
                    ) : (
                      <LockIcon />
                    )}
                  </TableCell>
                </TableRow>
              }) : <TableRow ><TableCell colSpan={12} sx={{
                textAlign: "center",
                fontWeight: 600
              }}>{`No Apps found for this ${props.topAppsByCountry ? `category and country`:'search'}`}</TableCell></TableRow>}
          </TableBody>
          <TableFooter >
          <TableRow>
            <TablePagination
              rowsPerPageOptions={[10, 25, 50,{ label: 'All', value: -1 }]}
              count={props.apps.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={((event, newPage) => setPage(newPage))}
              onRowsPerPageChange={handleChangeRowsPerPage}
              slotProps={{
                select: {
                  inputProps: {
                    'aria-label': 'rows per page',
                  },
                  native: true,
                },
              }}
              ActionsComponent={TablePaginationActions}
            />
          </TableRow>
        </TableFooter>
        </Table>
      </TableContainer>
       :  <GridViewApp apps={props.apps} isForCountry={props.topAppsByCountry}/>
      }
    </Box>
  );
};


const mapStateToProps = ({topApps}) => ({
  category: topApps.category,
});

export default connect(mapStateToProps)(AppCard);