"use strict";
import mongoose, { Schema, models } from "mongoose";

const developerSchema = new Schema(
  {
    developer: { type: String, required: true, unique: true },
    developerId: { type: String, unique: true, default: "" },
    appIds: {
      type: [String],
    },
    totalApps:{type: Number},
  },
  { timestamps: true }
);
developerSchema.index({ developer: 1});
mongoose.models = {};
const Developer =
  models?.Developer || mongoose.model("DeveloperApp", developerSchema);
export default Developer;
