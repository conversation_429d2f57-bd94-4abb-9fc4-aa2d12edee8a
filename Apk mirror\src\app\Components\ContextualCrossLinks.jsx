"use client";
import React from 'react';
import Link from 'next/link';
import { 
  generateAIToolsURL, 
  generateLatestVersionURL,
  generateOlderVersionsURL,
  generateAnchorText, 
  trackCrossLinkClick,
  isCrossLinkingEnabled,
  getCurrentSiteType,
  SITE_INFO
} from '../utils/crossLinking';

/**
 * Contextual Cross-Links Component for APK Pages
 * Shows relevant links to other sites based on app information
 */
const ContextualCrossLinks = ({ 
  appDetails, 
  variant = 'card', // 'card', 'banner', 'inline'
  className = '',
  maxLinks = 3 
}) => {
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const currentSite = getCurrentSiteType();
  const links = [];

  // Generate appropriate links based on current site
  if (currentSite === 'all-versions') {
    const latestURL = generateLatestVersionURL(appDetails);
    const aiToolsURL = generateAIToolsURL(appDetails);
    
    if (latestURL) {
      links.push({
        type: 'latest-version',
        url: latestURL,
        text: 'Get Latest Version',
        description: `Download the newest version of ${appDetails?.title || 'this app'}`,
        icon: '🔄'
      });
    }
    if (aiToolsURL) {
      links.push({
        type: 'ai-tools',
        url: aiToolsURL,
        text: generateAnchorText('ai-tools', appDetails),
        description: `Discover AI tools for ${appDetails?.category || 'productivity'}`,
        icon: '🤖'
      });
    }
  } else if (currentSite === 'latest-only') {
    const olderVersionsURL = generateOlderVersionsURL(appDetails);
    const aiToolsURL = generateAIToolsURL(appDetails);
    
    if (olderVersionsURL) {
      links.push({
        type: 'older-versions',
        url: olderVersionsURL,
        text: 'Get Older Versions',
        description: `Download previous versions of ${appDetails?.title || 'this app'}`,
        icon: '📦'
      });
    }
    if (aiToolsURL) {
      links.push({
        type: 'ai-tools',
        url: aiToolsURL,
        text: generateAnchorText('ai-tools', appDetails),
        description: `Discover AI tools for ${appDetails?.category || 'productivity'}`,
        icon: '🤖'
      });
    }
  }

  // Add AppStoreSpy link
  if (appDetails?.appId) {
    links.push({
      type: 'app-analytics',
      url: `${SITE_INFO.APP_STORE_SPY.domain}/app-details/${appDetails.appId}?ref=apk`,
      text: 'View App Analytics',
      description: `Analyze ${appDetails?.title || 'this app'}'s performance and market data`,
      icon: '📊'
    });
  }

  if (links.length === 0) {
    return null;
  }

  const handleLinkClick = (link) => {
    trackCrossLinkClick('apk-mirror', link.type, {
      appId: appDetails?.appId,
      title: appDetails?.title,
      category: appDetails?.category
    });
  };

  // Card variant - prominent display
  if (variant === 'card') {
    return (
      <div className={`bg-gradient-to-br from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6 mt-6 ${className}`}>
        <div className="flex items-center mb-4">
          <span className="text-2xl mr-2">🚀</span>
          <h3 className="text-lg font-semibold text-gray-900">
            Explore More Resources
          </h3>
        </div>
        
        <p className="text-gray-600 mb-4">
          Discover additional tools and insights for {appDetails?.title || 'this app'}
        </p>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
          {links.slice(0, maxLinks).map((link, index) => (
            <Link
              key={index}
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="block p-4 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all duration-200 group"
              onClick={() => handleLinkClick(link)}
            >
              <div className="flex items-center mb-2">
                <span className="text-xl mr-2">{link.icon}</span>
                <span className="font-medium text-gray-900 group-hover:text-blue-600">
                  {link.text}
                </span>
              </div>
              <p className="text-sm text-gray-500">
                {link.description}
              </p>
            </Link>
          ))}
        </div>
      </div>
    );
  }

  // Banner variant - horizontal layout
  if (variant === 'banner') {
    return (
      <div className={`bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 mt-6 text-white ${className}`}>
        <div className="flex items-center justify-between flex-wrap gap-4">
          <div>
            <h3 className="text-lg font-semibold mb-2">
              🌟 Expand Your Experience
            </h3>
            <p className="opacity-90">
              Get more versions or discover AI tools for {appDetails?.category || 'productivity'}
            </p>
          </div>
          
          <div className="flex gap-3 flex-wrap">
            {links.slice(0, 2).map((link, index) => (
              <Link
                key={index}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 rounded-md hover:bg-opacity-30 transition-all duration-200 font-medium"
                onClick={() => handleLinkClick(link)}
              >
                <span className="mr-2">{link.icon}</span>
                {link.text}
              </Link>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Inline variant - compact chips
  if (variant === 'inline') {
    return (
      <div className={`mt-4 ${className}`}>
        <p className="text-sm text-gray-600 mb-2">Related resources:</p>
        <div className="flex gap-2 flex-wrap">
          {links.slice(0, maxLinks).map((link, index) => (
            <Link
              key={index}
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm hover:bg-blue-200 transition-colors duration-200"
              onClick={() => handleLinkClick(link)}
            >
              <span className="mr-1">{link.icon}</span>
              {link.text}
            </Link>
          ))}
        </div>
      </div>
    );
  }

  return null;
};

/**
 * App Overview Cross-Links Component for APK pages
 * Specifically designed for the overview section of app details
 */
export const AppOverviewCrossLinks = ({ appDetails, className = '' }) => {
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const currentSite = getCurrentSiteType();
  const links = [];

  // Generate links based on current site
  if (currentSite === 'all-versions') {
    const latestURL = generateLatestVersionURL(appDetails);
    const aiToolsURL = generateAIToolsURL(appDetails);
    
    if (latestURL) {
      links.push({
        type: 'latest-version',
        url: latestURL,
        text: 'Latest Version',
        description: 'Get the newest release',
        icon: '🔄'
      });
    }
    if (aiToolsURL) {
      links.push({
        type: 'ai-tools',
        url: aiToolsURL,
        text: 'AI Tools',
        description: 'Discover related AI tools',
        icon: '🤖'
      });
    }
  }

  // Add AppStoreSpy link
  if (appDetails?.appId) {
    links.push({
      type: 'app-analytics',
      url: `${SITE_INFO.APP_STORE_SPY.domain}/app-details/${appDetails.appId}?ref=apk`,
      text: 'App Analytics',
      description: 'View performance insights',
      icon: '📊'
    });
  }

  if (links.length === 0) {
    return null;
  }

  return (
    <div className={`mt-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Related Resources
      </h3>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {links.map((link, index) => (
          <Link
            key={index}
            href={link.url}
            target="_blank"
            rel="noopener noreferrer"
            className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-lg transition-all duration-200 group"
            onClick={() => trackCrossLinkClick('apk-mirror', link.type, {
              appId: appDetails?.appId,
              title: appDetails?.title,
              category: appDetails?.category
            })}
          >
            <div className="flex items-center mb-2">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3 group-hover:bg-blue-200 transition-colors">
                <span className="text-lg">{link.icon}</span>
              </div>
              <span className="font-medium text-gray-900 group-hover:text-blue-600">
                {link.text}
              </span>
            </div>
            <p className="text-sm text-gray-500">
              {link.description}
            </p>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default ContextualCrossLinks;
