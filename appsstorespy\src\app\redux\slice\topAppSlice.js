import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

const initialState = {
  topFreeApps: [],
  topGrossingApps: [],
  topPaidApps: [],
  topNewPaidApps: [],
  topNewFreeApps: [],
  searchApps:[],
  topAppsLoading: true,
  searchAppsLoading:false,
  searchResultsAvailable: false,
  error: null,
  category:"APPLICATION",
  searchParams: {},
  userCountry: {
    countryCode:'',
    country:''
  }
};

export const fetchTopAppDetails = createAsyncThunk(
  "appRanks/fetchTopAppDetails",
  async ({ countryCode, collection = "", category = "" }) => {
    try {
      const response = await axios.post("/api/app_ranks", {
        countryCode,
        collection,
        category
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }
);
export const searchApps = createAsyncThunk(
  "appRanks/searchApps",
  async ({ searchTerm, storeType, price, countryCode }) => {
    try {
      const response = await axios.post("/api/search", {
        searchTerm,
        storeType,
        price,
        countryCode,
      });
      return {
        savedApps: response.data.savedApps,
        searchParams: {
          searchTerm,
          storeType,
          price,
          countryCode:countryCode || "US",
        },
      };
    } catch (error) {
      throw error;
    }
  }
);

const topAppSlice = createSlice({
  name: "appRanks",
  initialState,
  reducers: {
    resetTopAppDetails(state) {
      state.topFreeApps = [];
      state.topGrossingApps = [];
      state.topPaidApps = [];
      state.topNewFreeApps =[];
      state.topNewPaidApps =[];
    },
    resetSearchApps(state) {
      state.searchApps = [];
      state.searchAppsLoading = false;
      state.searchParams = {};
      state.searchResultsAvailable = false;
    },
    updateCategory(state, action) {
      state.category = action.payload;
    },
    resetCategory(state) {
      state.category = initialState.category; 
    },
    selectedUserCountry(state,action){
      state.userCountry = action.payload;
    }
    
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchTopAppDetails.pending, (state) => {
        state.topAppsLoading = true; 
        state.error = null;
      })
      .addCase(fetchTopAppDetails.fulfilled, (state, action) => {
        state.topAppsLoading = false;
        state.error = null;

        if (action.payload.topFreeApps) {
          state.topFreeApps = action.payload.topFreeApps;
        }
        if (action.payload.topGrossingApps) {
          state.topGrossingApps = action.payload.topGrossingApps;
        }
        if (action.payload.topPaidApps) {
          state.topPaidApps = action.payload.topPaidApps;
        }
        if (action.payload.topNewPaidApps) {
          state.topNewPaidApps = action.payload.topNewPaidApps;
        }
        if (action.payload.topNewFreeApps) {
          state.topNewFreeApps = action.payload.topNewFreeApps;
        }
      })
      .addCase(fetchTopAppDetails.rejected, (state, action) => {
        state.topAppsLoading = false; 
        state.error = action.error.message;
      })
      .addCase(searchApps.pending, (state) => {
        state.searchAppsLoading = true;
        state.error = null;
      })
      .addCase(searchApps.fulfilled, (state, action) => {
        state.searchAppsLoading = false;
        state.error = null;
        state.searchApps = action.payload.savedApps;
        state.searchParams = action.payload.searchParams;
        state.searchResultsAvailable = true;
      })
      .addCase(searchApps.rejected, (state, action) => {
        state.searchAppsLoading = false;
        state.error = action.error.message;
      });
  },
});
export const {resetTopAppDetails, resetSearchApps, updateCategory,resetCategory,selectedUserCountry} = topAppSlice.actions;
export default topAppSlice.reducer;
