"use client";
import  React, { useState, useEffect} from "react";
import { useTheme } from "@mui/material/styles";
import {
    Box,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableFooter,
    TablePagination,
    TableRow,
    Paper,
    IconButton,
    TableHead,
    TextField,
    Typography
  } from "@mui/material";
import {LastPage, KeyboardArrowRight, KeyboardArrowLeft, FirstPage, ArrowUpward} from "@mui/icons-material";
import { countries } from "@/app/utils/countries";

function TablePaginationActions(props) {
  const theme = useTheme();
  const { count, page, rowsPerPage, onPageChange } = props;

  const handleFirstPageButtonClick = (event) => {
    onPageChange(event, 0);
  };

  const handleBackButtonClick = (event) => {
    onPageChange(event, page - 1);
  };

  const handleNextButtonClick = (event) => {
    onPageChange(event, page + 1);
  };

  const handleLastPageButtonClick = (event) => {
    onPageChange(event, Math.max(0, Math.ceil(count / rowsPerPage) - 1));
  };

  return (
    <Box sx={{ flexShrink: 0, ml: 2.5 }}>
      <IconButton
        onClick={handleFirstPageButtonClick}
        disabled={page === 0}
        aria-label="first page"
      >
        {theme.direction === "rtl" ? <LastPage /> : <FirstPage />}
      </IconButton>
      <IconButton
        onClick={handleBackButtonClick}
        disabled={page === 0}
        aria-label="previous page"
      >
        {theme.direction === "rtl" ? (
          <KeyboardArrowRight />
        ) : (
          <KeyboardArrowLeft />
        )}
      </IconButton>
      <IconButton
        onClick={handleNextButtonClick}
        disabled={page >= Math.ceil(count / rowsPerPage) - 1}
        aria-label="next page"
      >
        {theme.direction === "rtl" ? (
          <KeyboardArrowLeft />
        ) : (
          <KeyboardArrowRight />
        )}
      </IconButton>
      <IconButton
        onClick={handleLastPageButtonClick}
        disabled={page >= Math.ceil(count / rowsPerPage) - 1}
        aria-label="last page"
      >
        {theme.direction === "rtl" ? <FirstPage /> : <LastPage />}
      </IconButton>
    </Box>
  );
}


const TopAppRankTable = ({ app }) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [countryIcon, setCountryIcon] = useState(false);
  const [FreeIcon, setFreeIcon] = useState(false);
  const [PaidIcon, setPaidIcon] = useState(false);
  const [GrossingIcon, setGrossingIcon] = useState(false);
  const [TopNewFreeIcon, setTopNewFreeIcon] = useState(false);
  const [TopNewPaidIcon, setTopNewPaidIcon] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [sortedApp, setSortedApp] = useState([]);
  const [sortColumn, setSortColumn] = useState(null);
  const [countrySortDirection, setCountrySortDirection] = useState("asc");

  const getValueByColumn = (appData, column) => {
    switch (column) {
      case "TOP_FREE":
        return appData.collectionName === "TOP_FREE" ? appData.position : "-";
      case "TOP_PAID":
        return appData.collectionName === "TOP_PAID" ? appData.position : "-";
      case "GROSSING":
        return appData.collectionName === "GROSSING" ? appData.position : "-";
      case "TOP_NEW_FREE":
        return appData.collectionName === "TOP_NEW_FREE" ? appData.position : "-";
      case "TOP_NEW_PAID":
        return appData.collectionName === "TOP_NEW_PAID" ? appData.position : "-";
      default:
        return "";
    }
  };

  const getUniqueCountriesLength = (data) => {
    const uniqueCountries = new Set(data.map(item => item.countryCode));
    return uniqueCountries.size;
  }
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  const filterData = (apps, searchVal) => {
    const filteredApps = apps.filter((appData) => {
      const countryName = getCountryName(appData.countryCode);
      return countryName
        ? countryName.toLowerCase().includes(searchVal.toLowerCase())
        : false;
    });
    setSortedApp(filteredApps);
  };


  useEffect(() => {
    filterData(app, searchValue);
  }, [app, searchValue]);

  const [sortDirections, setSortDirections] = useState({
    "TOP_FREE": "asc",
    "TOP_PAID": "asc",
    "GROSSING": "asc",
    "TOP_NEW_FREE": "asc",
    "TOP_NEW_PAID": "asc",
  });

   const btnStyle = (icon) => {
    return {
      transform: icon ? "rotate(180deg)" : "rotate(0deg)",
      transition: "transform 0.3s",
      padding:"5px",
      fontSize:"1.2rem"
    }
   }

  const handleSort = (column) => {
    const sortDirection = sortColumn === column ? sortDirections[column] === "asc" ? "desc" : "asc" : "asc";
    const sortedApps = [...sortedApp].sort((a, b) => {
      const valueA = getValueByColumn(a, column);
      const valueB = getValueByColumn(b, column);
      if (valueA === "-") return sortDirection === "asc" ? 1 : -1;
      if (valueB === "-") return sortDirection === "asc" ? -1 : 1;
      return sortDirection === "asc" ? valueA - valueB : valueB - valueA;
    });
  
    setSortedApp(sortedApps);
    setSortColumn(column);
    setSortDirections({ ...sortDirections, [column]: sortDirection });
  
    setFreeIcon(column === "TOP_FREE");
    setCountryIcon(column === "COUNTRY");
    setPaidIcon(column === "TOP_PAID");
    setGrossingIcon(column === "GROSSING");
    setTopNewFreeIcon(column === "TOP_NEW_FREE");
    setTopNewPaidIcon(column === "TOP_NEW_PAID");
  };

  const handleCountrySort = () => {
    if (countryIcon) {
      setFreeIcon(false);
      setPaidIcon(false);
      setGrossingIcon(false);
      setTopNewPaidIcon(false);
      setTopNewFreeIcon(false);
    }
    setCountryIcon(!countryIcon);
    const newCountrySortDirection =
      countrySortDirection === "asc" ? "desc" : "asc";
    setCountrySortDirection(newCountrySortDirection);

    const sortedApps = [...sortedApp].sort((a, b) => {
      const countryCodeA = a.countryCode.toLowerCase();
      const countryCodeB = b.countryCode.toLowerCase();

      if (newCountrySortDirection === "asc") {
        return countryCodeA.localeCompare(countryCodeB);
      } else {
        return countryCodeB.localeCompare(countryCodeA);
      }
    });

    setSortedApp(sortedApps);
  };

  const getCountryName = (countryCode) => {
    const country = countries.find((c) => c.code === countryCode);
    return country ? country.label : "";
  };

  const SortableTableCell = ({ label, collectionName, handleSort, icon }) => (
    <TableCell sx={{ fontWeight: "bold", textTransform: "capitalize" }}>
      <Box display="flex" alignItems="center">
        <Typography>{label}</Typography>
        <IconButton onClick={() => handleSort(collectionName)} style={btnStyle(icon)}>
          <ArrowUpward fontSize="small" />
        </IconButton>
      </Box>
    </TableCell>
  );

  const groupedData = sortedApp.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).reduce((acc, appData) => {
    if (!acc[appData.countryCode]) {
      acc[appData.countryCode] = [];
    }
    acc[appData.countryCode].push(appData);
    return acc;
  }, {});

  return (
      <TableContainer component={Paper}>
        <Table aria-label="simple table">
          <TableHead>
            <TableRow
              sx={{
                borderTop: "none",
              }}
            >
              <TableCell>
                <Box
                  sx={{
                    fontWeight: "bold",
                    display: "flex",
                    flexWrap: "nowrap",
                    textTransform: "capitalize",
                    alignItems:"center"
                  }}
                >
                  <Box sx={{ mt: 1 }}>
                    Country
                    <IconButton
                      onClick={handleCountrySort}
                      style={btnStyle(countryIcon)}
                    >
                     <ArrowUpward  fontSize="small"/> 
                    </IconButton>
                  </Box>
                  <Box sx={{ ml: 2 }}>
                    <TextField
                      size="small"
                      id="outlined-basic"
                      label="Search"
                      variant="outlined"
                      value={searchValue}
                      onChange={(e)=> setSearchValue(e.target.value)}
                    />
                  </Box>
                </Box>
              </TableCell>

                <SortableTableCell
              label="Top Free"
              collectionName="TOP_FREE"
              handleSort={handleSort}
              icon={FreeIcon}
            />
              <SortableTableCell
              label="Top Paid"
              collectionName="TOP_PAID"
              handleSort={handleSort}
              icon={PaidIcon}
            />
            <SortableTableCell
              label="Top Grossing"
              collectionName="GROSSING"
              handleSort={handleSort}
              icon={GrossingIcon}
            />
            <SortableTableCell
              label="Top New Free"
              collectionName="TOP_NEW_FREE"
              handleSort={handleSort}
              icon={TopNewFreeIcon}
            />
            <SortableTableCell
              label="Top New Paid"
              collectionName="TOP_NEW_PAID"
              handleSort={handleSort}
              icon={TopNewPaidIcon}
            />

              
            </TableRow>
          </TableHead>
          <TableBody>
          {Object.entries(groupedData).map(([countryCode, appDataForCountry], index) => (
                  <TableRow
                    key={countryCode}
                    sx={{
                      borderBottom: 'none',
                      color: '#716c6c',
                      backgroundColor: index % 2 === 0 ? '#eff9ff' : '#fff',
                      '&:hover': {
                        backgroundColor: '#daf1ff',
                      },
                    }}
                  >
                    <TableCell>
                      <Box display="flex">
                        <img
                          loading="lazy"
                          srcSet={`https://flagcdn.com/w40/${countryCode.toLowerCase()}.png 2x`}
                          src={`https://flagcdn.com/w20/${countryCode.toLowerCase()}.png`}
                          alt={getCountryName(countryCode)}
                          title={getCountryName(countryCode)}
                          style={{ marginRight: '0.5rem' }}
                        />
                        <Typography variant="h3" sx={{ fontSize: '15px', overflow: 'hidden' }}>
                          {getCountryName(countryCode)}
                        </Typography>
                      </Box>
                    </TableCell>
                    {['TOP_FREE', 'TOP_PAID', 'GROSSING', 'TOP_NEW_FREE', 'TOP_NEW_PAID'].map(
                      (collection) => (
                        <TableCell key={`${countryCode}-${collection}`}>
                          {appDataForCountry
                            .find((appData) => appData.collectionName === collection)
                            ?.position || '-'}
                        </TableCell>
                      )
                    )}
                  </TableRow>
                ))}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TablePagination
                rowsPerPageOptions={[
                  5,
                  10,
                  25,
                  50,
                  { label: "All", value: -1 },
                ]}
                count={getUniqueCountriesLength(app)}
                rowsPerPage={rowsPerPage}
                page={page}
                slotProps={{
                  select: {
                    inputProps: {
                      "aria-label": "rows per page",
                    },
                    native: true,
                  },
                }}
                onPageChange={((event, newPage) => setPage(newPage))}
                onRowsPerPageChange={handleChangeRowsPerPage}
                ActionsComponent={TablePaginationActions}
              />
            </TableRow>
          </TableFooter>
        </Table>
      </TableContainer>
  );
};

export default TopAppRankTable;
