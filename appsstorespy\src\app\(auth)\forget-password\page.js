"use client";
import React, {useState } from "react";
import {
  <PERSON><PERSON>,
  Box,
  Typo<PERSON>,
  FormHelperText,
  TextField,
  Alert,
  CircularProgress,
  Divider
} from "@mui/material";
import Link from "next/link";
import axios from "axios";

function page() {
  const [userDetails, setUserDetails] = useState({
    email: "",
  });
  const [invalid, setInvalid] = useState("");
  const [successMsg, setSuccessMsg] = useState("");
  const [loginError, setLoginError] = useState({
    emailError: "",
  });
  const [pending, setPending] = useState(false);
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;


  const handleOnChange = (e) => {
    const { name, value } = e.target;
    setUserDetails((prevState) => ({
      ...prevState,
      [name]: value,
    }));

    if (name === "email") {
      if (!value.trim()) {
        setLoginError((prevError) => ({
          ...prevError,
          emailError: "Email is required*",
        }));
      } else if (!emailRegex.test(value)) {
        setLoginError((prevError) => ({
          ...prevError,
          emailError: "Invalid email format",
        }));
      } else {
        setLoginError((prevError) => ({
          ...prevError,
          emailError: "",
        }));
      }
    }

    setInvalid("");
  };
  const handleFormSubmit = async (e) => {
    e.preventDefault();
    if (!userDetails.email.trim() || !emailRegex.test(userDetails.email)) {
      setLoginError((prevState) => ({
        ...prevState,
        emailError: "Please enter your email",
      }));
      return;
    }
    try {
      setPending(true);
      const response = await axios.post(`/api/forget-password`, {
        email: userDetails.email,
      });

      if (response && response.status === 200) {
        setUserDetails({
          email: "",
        });
        setSuccessMsg("Please Check your email");
      } else {
        setInvalid(response.data.message);
      }
    } catch (error) {
      setInvalid("Something went wrong");
      console.error("Error:", error);
    }

    setLoginError({
      emailError: "",
    });

  };

 return (
  <Box
    sx={{
      display: "flex",
      minHeight: "100vh",
      backgroundColor: "#f8fafc"
    }}
  >
    {/* Left side - Illustration/Image */}
    <Box
      sx={{
        display: { xs: "none", md: "flex" },
        width: "50%",
        backgroundColor: "#00A3FF",
        justifyContent: "center",
        alignItems: "center",
        padding: "2rem"
      }}
    >
      <Box sx={{ maxWidth: "500px", textAlign: "center" }}>
        <Typography
          variant="h3"
          sx={{
            fontWeight: 800,
            color: "white",
            mb: 3,
            fontSize: "2.5rem"
          }}
        >
          Reset Your Password
        </Typography>
        <Typography
          variant="body1"
          sx={{
            color: "rgba(255,255,255,0.9)",
            fontSize: "1.1rem",
            lineHeight: 1.6
          }}
        >
          Enter your email address and we'll send you a link to reset your password.
        </Typography>
        <Box sx={{ mt: 4 }}>
                   <img
                     src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                     alt="App Analytics"
                     style={{ width: "100%", borderRadius: "8px" }}
                   />
                 </Box>
      </Box>
    </Box>

    {/* Right side - Form */}
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        width: { xs: "100%", md: "50%" },
        padding: { xs: "2rem", md: "4rem" }
      }}
    >
      <Box sx={{ mb: 4, textAlign: "center" }}>
        <Typography
          variant="h4"
          sx={{
            fontWeight: 700,
            color: "#101828",
            mb: 1
          }}
        >
          Forgot your password?
        </Typography>
        <Typography variant="body1" sx={{ color: "#667085" }}>
          Enter your email to receive a reset link
        </Typography>
      </Box>

      <Box
        sx={{
          width: "100%",
          maxWidth: "500px",
          margin: "0 auto",
          backgroundColor: "white",
          borderRadius: "12px",
          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.08)",
          padding: { xs: "1.5rem", sm: "2rem" }
        }}
      >
        {invalid && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {invalid}
          </Alert>
        )}

        {successMsg && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {successMsg}
          </Alert>
        )}

        <form onSubmit={handleFormSubmit}>
          <Box sx={{ mb: 3 }}>
            <TextField
              fullWidth
              name="email"
              label="Email"
              variant="outlined"
              onChange={handleOnChange}
              error={Boolean(loginError.emailError)}
              helperText={loginError.emailError}
            />
          </Box>

          <Button
            fullWidth
            variant="contained"
            size="large"
            type="submit"
            disabled={pending}
            sx={{
              backgroundColor: "#00A3FF",
              py: 1.5,
              borderRadius: "8px",
              textTransform: "none",
              fontSize: "1rem",
              fontWeight: 600,
              "&:hover": {
                backgroundColor: "#0088cc"
              }
            }}
          >
            {pending ? (
              <>
                <CircularProgress size={24} sx={{ color: "white", mr: 1 }} />
                Sending...
              </>
            ) : (
              "Send Reset Link"
            )}
          </Button>
        </form>

        <Box sx={{ 
          display: "flex", 
          justifyContent: "center",
          alignItems: "center",
          mt: 3,
          mb: 2
        }}>
          <Divider sx={{ flexGrow: 1 }} />
          <Typography variant="body2" sx={{ px: 2, color: "#98A2B3" }}>OR</Typography>
          <Divider sx={{ flexGrow: 1 }} />
        </Box>

        <Box sx={{ textAlign: "center" }}>
          <Typography variant="body2" sx={{ color: "#667085" }}>
            Remember your password?{" "}
            <Link
              href="/login"
              sx={{
                color: "#00A3FF",
                fontWeight: 600,
                textDecoration: "none",
                "&:hover": { textDecoration: "underline" }
              }}
            >
              Login here
            </Link>
          </Typography>
        </Box>
      </Box>

      <Box sx={{ mt: 4, textAlign: "center" }}>
        <Typography variant="body2" sx={{ color: "#98A2B3" }}>
          © {new Date().getFullYear()} AppsStoreSpy. All rights reserved.
        </Typography>
      </Box>
    </Box>
  </Box>
);
}

export default page;
