"use client";
import React from 'react';

/**
 * Semantic HTML Layout Component for APK Mirror
 * Provides proper semantic structure for better SEO and accessibility
 */
const SemanticLayout = ({ 
  children,
  headerContent,
  navContent,
  asideContent,
  footerContent,
  className = '',
  mainClassName = '',
  role = null
}) => {
  return (
    <div className={`semantic-layout flex flex-col min-h-screen ${className}`}>
      {/* Header Section */}
      {headerContent && (
        <header 
          role="banner"
          className="sticky top-0 z-50 bg-white border-b border-gray-200 shadow-sm"
        >
          {headerContent}
        </header>
      )}

      {/* Navigation Section */}
      {navContent && (
        <nav 
          role="navigation"
          aria-label="Main navigation"
          className="bg-gray-50 border-b border-gray-200"
        >
          {navContent}
        </nav>
      )}

      {/* Main Content Area */}
      <div className="flex flex-1 flex-col md:flex-row">
        {/* Sidebar/Aside Content */}
        {asideContent && (
          <aside 
            role="complementary"
            aria-label="Sidebar content"
            className="w-full md:w-80 bg-white border-r border-gray-200 p-4"
          >
            {asideContent}
          </aside>
        )}

        {/* Main Content */}
        <main 
          role={role || "main"}
          aria-label="Main content"
          className={`flex-1 p-4 md:p-6 bg-gray-50 ${mainClassName}`}
        >
          {children}
        </main>
      </div>

      {/* Footer Section */}
      {footerContent && (
        <footer 
          role="contentinfo"
          className="mt-auto bg-white border-t border-gray-200"
        >
          {footerContent}
        </footer>
      )}
    </div>
  );
};

/**
 * Article Layout Component for APK Mirror
 * Semantic structure for article/blog content
 */
export const ArticleLayout = ({ 
  title,
  subtitle,
  author,
  publishDate,
  lastModified,
  content,
  relatedContent,
  breadcrumbs,
  tags = [],
  className = ''
}) => {
  return (
    <article 
      className={`article-layout ${className}`}
      itemScope 
      itemType="https://schema.org/Article"
    >
      {/* Breadcrumbs */}
      {breadcrumbs && (
        <nav aria-label="Breadcrumb" className="mb-4">
          <ol 
            className="flex items-center space-x-2 text-sm text-gray-600"
            itemScope 
            itemType="https://schema.org/BreadcrumbList"
          >
            {breadcrumbs.map((crumb, index) => (
              <li 
                key={index}
                itemProp="itemListElement" 
                itemScope 
                itemType="https://schema.org/ListItem"
                className="flex items-center"
              >
                {index > 0 && <span className="mx-2 text-gray-400">/</span>}
                {crumb.href ? (
                  <a 
                    href={crumb.href}
                    itemProp="item"
                    className="hover:text-blue-600 transition-colors"
                  >
                    <span itemProp="name">{crumb.name}</span>
                  </a>
                ) : (
                  <span itemProp="name" className="text-gray-900 font-medium">{crumb.name}</span>
                )}
                <meta itemProp="position" content={index + 1} />
              </li>
            ))}
          </ol>
        </nav>
      )}

      {/* Article Header */}
      <header className="mb-6">
        <h1 
          className="text-3xl font-bold text-gray-900 mb-2"
          itemProp="headline"
        >
          {title}
        </h1>
        
        {subtitle && (
          <h2 
            className="text-xl text-gray-600 mb-4"
            itemProp="alternativeHeadline"
          >
            {subtitle}
          </h2>
        )}

        {/* Article Meta */}
        <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
          {author && (
            <div 
              itemProp="author" 
              itemScope 
              itemType="https://schema.org/Person"
            >
              By <span itemProp="name" className="font-medium">{author}</span>
            </div>
          )}
          
          {publishDate && (
            <time 
              dateTime={publishDate}
              itemProp="datePublished"
              className="flex items-center"
            >
              📅 {new Date(publishDate).toLocaleDateString()}
            </time>
          )}
          
          {lastModified && (
            <time 
              dateTime={lastModified}
              itemProp="dateModified"
              className="flex items-center"
            >
              🔄 Updated: {new Date(lastModified).toLocaleDateString()}
            </time>
          )}
        </div>

        {/* Tags */}
        {tags.length > 0 && (
          <div className="mt-4">
            <span className="text-sm text-gray-500 mr-2">Tags:</span>
            <div className="inline-flex flex-wrap gap-2">
              {tags.map((tag, index) => (
                <span 
                  key={index}
                  className="inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs font-medium"
                  itemProp="keywords"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}
      </header>

      {/* Article Content */}
      <section 
        className="prose max-w-none mb-8"
        itemProp="articleBody"
      >
        {content}
      </section>

      {/* Related Content */}
      {relatedContent && (
        <aside 
          className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200"
          aria-label="Related content"
        >
          <h3 className="text-lg font-semibold mb-4 text-gray-900">Related Content</h3>
          {relatedContent}
        </aside>
      )}
    </article>
  );
};

/**
 * App Details Layout Component for APK pages
 * Semantic structure for app detail pages
 */
export const AppDetailsLayout = ({ 
  appInfo,
  screenshots,
  description,
  downloadSection,
  specifications,
  versions,
  relatedApps,
  className = ''
}) => {
  return (
    <article 
      className={`app-details-layout ${className}`}
      itemScope 
      itemType="https://schema.org/SoftwareApplication"
    >
      {/* App Header */}
      <header className="mb-8 bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-start gap-6">
          {appInfo.icon && (
            <img 
              src={appInfo.icon}
              alt={`${appInfo.title} app icon`}
              className="w-20 h-20 rounded-xl shadow-md"
              itemProp="image"
            />
          )}
          <div className="flex-1">
            <h1 
              className="text-3xl font-bold text-gray-900 mb-2"
              itemProp="name"
            >
              {appInfo.title}
            </h1>
            {appInfo.developer && (
              <div 
                className="text-lg text-gray-600 mb-3"
                itemProp="author"
                itemScope
                itemType="https://schema.org/Organization"
              >
                by <span itemProp="name" className="font-medium">{appInfo.developer}</span>
              </div>
            )}
            <div className="flex flex-wrap gap-2">
              {appInfo.category && (
                <span 
                  className="inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium"
                  itemProp="applicationCategory"
                >
                  {appInfo.category}
                </span>
              )}
              {appInfo.version && (
                <span 
                  className="inline-block bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium"
                  itemProp="softwareVersion"
                >
                  v{appInfo.version}
                </span>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Download Section */}
      {downloadSection && (
        <section aria-label="Download" className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Download APK</h2>
          <div className="bg-white rounded-lg shadow-sm p-6">
            {downloadSection}
          </div>
        </section>
      )}

      {/* Screenshots Section */}
      {screenshots && (
        <section aria-label="Screenshots" className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Screenshots</h2>
          <div className="bg-white rounded-lg shadow-sm p-6">
            {screenshots}
          </div>
        </section>
      )}

      {/* Description Section */}
      {description && (
        <section aria-label="Description" className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">About {appInfo.title}</h2>
          <div className="bg-white rounded-lg shadow-sm p-6" itemProp="description">
            {description}
          </div>
        </section>
      )}

      {/* Specifications Section */}
      {specifications && (
        <section aria-label="Specifications" className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">App Information</h2>
          <div className="bg-white rounded-lg shadow-sm p-6">
            {specifications}
          </div>
        </section>
      )}

      {/* Versions Section */}
      {versions && (
        <section aria-label="All versions" className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">All Versions</h2>
          <div className="bg-white rounded-lg shadow-sm p-6">
            {versions}
          </div>
        </section>
      )}

      {/* Related Apps */}
      {relatedApps && (
        <aside aria-label="Related apps" className="mt-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Related Apps</h2>
          <div className="bg-white rounded-lg shadow-sm p-6">
            {relatedApps}
          </div>
        </aside>
      )}
    </article>
  );
};

export default SemanticLayout;
