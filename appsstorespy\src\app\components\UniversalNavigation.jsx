"use client";
import React, { useState } from 'react';
import Link from 'next/link';
import { Box, Button, Menu, MenuItem, Typography, Chip } from '@mui/material';
import { Language, Apps, SmartToy, Analytics } from '@mui/icons-material';
import { DOMAINS, SITE_INFO, getCurrentSiteType, isCrossLinkingEnabled } from '../utils/crossLinking';

/**
 * Universal Navigation Component for Cross-Site Linking (Material-UI version)
 * Provides SEO-friendly navigation between all three sites
 */
const UniversalNavigation = ({ 
  variant = 'header', // 'header', 'footer', 'dropdown'
  className = '',
  showDescriptions = false,
  currentSite = null 
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const currentSiteType = currentSite || getCurrentSiteType();
  
  // Get all sites except current one for navigation
  const otherSites = Object.entries(SITE_INFO).filter(([key]) => {
    if (currentSiteType === 'all-versions') return key !== 'APK_ALL_VERSIONS';
    if (currentSiteType === 'latest-only') return key !== 'APK_LATEST';
    if (currentSiteType === 'ai-tools') return key !== 'AI_TOOLS';
    return key !== 'APP_STORE_SPY';
  });

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  // Get icon for each site
  const getSiteIcon = (key) => {
    switch (key) {
      case 'APK_ALL_VERSIONS':
      case 'APK_LATEST':
        return <Apps sx={{ fontSize: 20 }} />;
      case 'AI_TOOLS':
        return <SmartToy sx={{ fontSize: 20 }} />;
      case 'APP_STORE_SPY':
        return <Analytics sx={{ fontSize: 20 }} />;
      default:
        return <Language sx={{ fontSize: 20 }} />;
    }
  };

  // Header variant - horizontal navigation
  if (variant === 'header') {
    return (
      <Box className={className} component="nav" aria-label="Cross-site navigation">
        {/* Desktop navigation */}
        <Box sx={{ display: { xs: 'none', md: 'flex' }, alignItems: 'center', gap: 3 }}>
          {otherSites.map(([key, site]) => (
            <Button
              key={key}
              href={site.domain}
              target="_blank"
              rel="noopener noreferrer"
              startIcon={getSiteIcon(key)}
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  color: 'primary.main',
                  backgroundColor: 'rgba(0, 163, 255, 0.04)'
                },
                textTransform: 'none',
                fontWeight: 500
              }}
              title={`Visit ${site.name} - ${site.description}`}
            >
              {site.name}
            </Button>
          ))}
        </Box>
        
        {/* Mobile navigation */}
        <Box sx={{ display: { xs: 'block', md: 'none' } }}>
          <Button
            onClick={handleMenuOpen}
            startIcon={<Language />}
            sx={{
              color: 'text.secondary',
              '&:hover': { color: 'primary.main' },
              textTransform: 'none'
            }}
            aria-expanded={Boolean(anchorEl)}
            aria-haspopup="true"
          >
            Our Sites
          </Button>
          
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
            PaperProps={{
              sx: { minWidth: 280 }
            }}
          >
            {otherSites.map(([key, site]) => (
              <MenuItem
                key={key}
                component="a"
                href={site.domain}
                target="_blank"
                rel="noopener noreferrer"
                onClick={handleMenuClose}
                sx={{ py: 2 }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                  {getSiteIcon(key)}
                  <Box>
                    <Typography variant="body2" fontWeight={500}>
                      {site.name}
                    </Typography>
                    {showDescriptions && (
                      <Typography variant="caption" color="text.secondary">
                        {site.description}
                      </Typography>
                    )}
                  </Box>
                </Box>
              </MenuItem>
            ))}
          </Menu>
        </Box>
      </Box>
    );
  }

  // Footer variant - vertical list with descriptions
  if (variant === 'footer') {
    return (
      <Box className={className}>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          Our Network
        </Typography>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {otherSites.map(([key, site]) => (
            <Button
              key={key}
              href={site.domain}
              target="_blank"
              rel="noopener noreferrer"
              startIcon={getSiteIcon(key)}
              sx={{
                justifyContent: 'flex-start',
                textAlign: 'left',
                color: 'text.secondary',
                '&:hover': {
                  color: 'primary.main',
                  backgroundColor: 'rgba(0, 163, 255, 0.04)'
                },
                textTransform: 'none',
                p: 1.5,
                borderRadius: 1
              }}
              title={`Visit ${site.name} - ${site.description}`}
            >
              <Box>
                <Typography variant="body2" fontWeight={500}>
                  {site.name}
                </Typography>
                <Typography variant="caption" color="text.secondary" display="block">
                  {site.description}
                </Typography>
              </Box>
            </Button>
          ))}
        </Box>
      </Box>
    );
  }

  // Dropdown variant - compact dropdown menu
  if (variant === 'dropdown') {
    return (
      <Box className={className}>
        <Button
          onClick={handleMenuOpen}
          variant="outlined"
          startIcon={<Language />}
          sx={{
            textTransform: 'none',
            borderColor: 'divider',
            color: 'text.primary'
          }}
          aria-expanded={Boolean(anchorEl)}
          aria-haspopup="true"
        >
          Explore Our Sites
        </Button>

        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          PaperProps={{
            sx: { minWidth: 320, maxWidth: 400 }
          }}
        >
          {otherSites.map(([key, site]) => (
            <MenuItem
              key={key}
              component="a"
              href={site.domain}
              target="_blank"
              rel="noopener noreferrer"
              onClick={handleMenuClose}
              sx={{ py: 2 }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                {getSiteIcon(key)}
                <Box sx={{ flex: 1 }}>
                  <Typography variant="body2" fontWeight={500}>
                    {site.name}
                  </Typography>
                  <Typography variant="caption" color="text.secondary" display="block">
                    {site.description}
                  </Typography>
                </Box>
              </Box>
            </MenuItem>
          ))}
        </Menu>
      </Box>
    );
  }

  return null;
};

/**
 * Cross-Site Banner Component (Material-UI version)
 * Prominent banner for promoting other sites
 */
export const CrossSiteBanner = ({ 
  className = '',
  targetSite = null,
  customMessage = null 
}) => {
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const currentSiteType = getCurrentSiteType();
  let bannerConfig = null;

  // Default banner configurations
  if (currentSiteType === 'app-store-spy' && !targetSite) {
    bannerConfig = {
      title: 'Discover More Tools & Downloads',
      description: 'Explore our AI tools platform and download Android APKs',
      sites: [SITE_INFO.AI_TOOLS, SITE_INFO.APK_ALL_VERSIONS],
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    };
  }

  if (!bannerConfig && !targetSite) {
    return null;
  }

  const sites = targetSite ? [SITE_INFO[targetSite]] : bannerConfig.sites;

  return (
    <Box
      className={className}
      sx={{
        background: bannerConfig?.gradient || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        borderRadius: 2,
        p: 4,
        textAlign: 'center',
        color: 'white'
      }}
    >
      <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
        {customMessage || bannerConfig?.title || 'Explore Our Network'}
      </Typography>
      <Typography variant="body1" sx={{ mb: 3, opacity: 0.9 }}>
        {bannerConfig?.description || 'Discover more tools and resources across our platform'}
      </Typography>
      <Box sx={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'center', gap: 2 }}>
        {sites.map((site, index) => (
          <Button
            key={index}
            href={site.domain}
            target="_blank"
            rel="noopener noreferrer"
            variant="contained"
            sx={{
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              color: 'white',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.3)'
              },
              textTransform: 'none',
              fontWeight: 500
            }}
          >
            Visit {site.name}
          </Button>
        ))}
      </Box>
    </Box>
  );
};

export default UniversalNavigation;
