import { NextResponse } from "next/server";
import gplay from "google-play-scraper";
import App from "@/app/database/appData";
import mongoose from "mongoose";
import AppRank from "@/app/database/appRanks";
import connectDB from "@/app/database/mongoose";
import { formatUpdateDate,formatReleasedDate,formatInstalls,formatRatingsAndReviews } from "@/app/utils/calculation";

export const GET = async (request,res) => {
  const url = new URL(request.url);
  const appId = url.searchParams.get("appId");
  if (!appId) {
    return NextResponse.json({ error: "App ID is required" }, { status: 400 });
  }

  try {
    await connectDB();
    let appDetails;
    appDetails = await App.findOne({ appId: appId });
    if (!appDetails) {
      try{
        appDetails = await gplay.app({appId, fullDetail : true});
        await App.updateOne({ appId }, appDetails, { upsert: true });
      }catch{
        return NextResponse.json({ message: "App not found" }, { status: 404 });
      }
    }
    const [appRanksArray, analyticsData] = await Promise.all([
      AppRank.find({ appId }),
      await getAnalyticsData(appId),
    ]);
    const appRanks = appRanksArray.sort((a, b) => a.position - b.position).map(
      ({ position, countryCode, collectionName, category }) => ({
        position,
        countryCode,
        category,
        collectionName,
      })
    );
    const appDetailsInfo = {
      appId: appDetails.appId,
      adSupported: appDetails.adSupported,
      title: appDetails.title,
      summary:appDetails.summary,
      url: appDetails.url,
      version:appDetails.version,
      contentRating: appDetails.contentRating,
      description: appDetails.description,
      developer: appDetails.developer,
      free: appDetails.free,
      genre:appDetails.genre,
      updated:formatUpdateDate(appDetails.updated),
      video:appDetails.video,
      headerImage: appDetails.headerImage,
      histogram:appDetails.histogram,
      icon:appDetails.icon,
      maxInstalls:formatInstalls(appDetails.maxInstalls),
      offersIAP: appDetails.offersIAP,
      ratings:formatRatingsAndReviews(appDetails.ratings),
      linearRatings:appDetails.ratings,
      reviews:formatRatingsAndReviews(appDetails.reviews),
      recentChanges:appDetails.recentChanges,
      released:formatReleasedDate(appDetails.released),
      removed:appDetails?.removed,
      scoreText:appDetails.scoreText,
      score:appDetails.score,
      screenshots:appDetails.screenshots,
    };
   
    const formatAnalyticsData = analyticsData
    ? analyticsData
        .filter(appAnalytics => appAnalytics !== null) 
        .map(appAnalytics => {
          const formattedMonthlyTotal = {
            installs: formatInstalls(appAnalytics.monthlyTotal.installs),
            ratings: formatRatingsAndReviews(appAnalytics.monthlyTotal.ratings),
            reviews: formatRatingsAndReviews(appAnalytics.monthlyTotal.reviews),
            dailyInstalls: formatInstalls(appAnalytics.monthlyTotal.dailyInstalls)
          };
  
          return {
            appId: appAnalytics.appId,
            analytics: appAnalytics.analytics,
            monthlyTotal: formattedMonthlyTotal
          };
        })
    : [];
    const mergedData = {
      appDetails: appDetailsInfo,
      appRanks,
      analyticsData:formatAnalyticsData,
    };
    return NextResponse.json({ app: mergedData }, { status: 200 });
  } catch (error) {
    return NextResponse.json(
      { message: "Error fetching app", error },
      { status: 500 }
    );
  }
};

const getAnalyticsData = async (appId) => {
  const date = new Date();
  const monthNames = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];
  const year = ("" + date.getFullYear()).slice(-2);
  const analyticsData = [];
  mongoose.models = {};
  const appSchema = new mongoose.Schema({
    appId: String,
    analytics: Object,
    monthlyTotal: Object,
  });
  for (let monthIndex = date.getMonth(); monthIndex >= 0; monthIndex--) {
    const collectionName = `${monthNames[monthIndex]}${year}`;
    // Retrieve data from the collection
    const AnalyticsModel = mongoose.model(collectionName, appSchema);
    const appAnalytics = await AnalyticsModel.findOne({ appId: appId });

    analyticsData.push(appAnalytics);
  }
  return analyticsData;
};
