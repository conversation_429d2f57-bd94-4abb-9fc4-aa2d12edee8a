import connectDB from "../../database/mongoose";
import { NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import User from "@/app/database/user";

export async function POST(req) {
  try {
    const { email, password } = await req.json();
    if (!email || !password) {
      return NextResponse.json(
        { error: "email and password is required" },
        { status: 400 }
      );
    }
    await connectDB();
    const userExists = await User.findOne({ email }).select("_id");
    if (userExists) {
      return NextResponse.json(
        { message: "Email already exists..!" },
        { status: 201 }
      );
    }
    const hashedPassword = await bcrypt.hash(password, 10);
    await User.create({ email, password: hashedPassword });

    return NextResponse.json(
      { message: "User registered successfully...!" },
      { status: 200 }
    );
  } catch (error) {
    return NextResponse.json(
      { message: "An error occurred while registering the user." },
      { status: 500 }
    );
  }
}
