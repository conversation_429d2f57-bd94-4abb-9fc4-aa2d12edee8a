"use client";
import React from 'react';
import { Box, Typography } from '@mui/material';

/**
 * Accessible Image Component with proper alt text and loading states
 */
export const AccessibleImage = ({ 
  src, 
  alt, 
  title,
  width, 
  height, 
  className = '',
  loading = 'lazy',
  onError,
  fallbackSrc = '/images/placeholder.jpg',
  ...props 
}) => {
  const [imgSrc, setImgSrc] = React.useState(src);
  const [hasError, setHasError] = React.useState(false);

  const handleError = (e) => {
    if (!hasError) {
      setHasError(true);
      setImgSrc(fallbackSrc);
      if (onError) onError(e);
    }
  };

  return (
    <img
      src={imgSrc}
      alt={alt}
      title={title}
      width={width}
      height={height}
      loading={loading}
      className={className}
      onError={handleError}
      {...props}
    />
  );
};

/**
 * Skip to Content Link for keyboard navigation
 */
export const SkipToContent = ({ targetId = 'main-content', className = '' }) => {
  return (
    <a
      href={`#${targetId}`}
      className={`sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50 ${className}`}
      style={{
        position: 'absolute',
        left: '-9999px',
        zIndex: 9999
      }}
      onFocus={(e) => {
        e.target.style.left = '1rem';
        e.target.style.top = '1rem';
      }}
      onBlur={(e) => {
        e.target.style.left = '-9999px';
      }}
    >
      Skip to main content
    </a>
  );
};

/**
 * Accessible Button Component with proper ARIA attributes
 */
export const AccessibleButton = ({ 
  children, 
  onClick, 
  disabled = false,
  ariaLabel,
  ariaDescribedBy,
  ariaExpanded,
  ariaControls,
  type = 'button',
  className = '',
  variant = 'primary',
  size = 'medium',
  ...props 
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
  };

  const sizeClasses = {
    small: 'px-3 py-1.5 text-sm',
    medium: 'px-4 py-2 text-base',
    large: 'px-6 py-3 text-lg'
  };

  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : '';

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      aria-expanded={ariaExpanded}
      aria-controls={ariaControls}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${disabledClasses} ${className}`}
      {...props}
    >
      {children}
    </button>
  );
};

/**
 * Accessible Heading Component with proper hierarchy
 */
export const AccessibleHeading = ({ 
  level = 1, 
  children, 
  className = '',
  id,
  ...props 
}) => {
  const HeadingTag = `h${level}`;
  
  const defaultClasses = {
    1: 'text-3xl font-bold text-gray-900',
    2: 'text-2xl font-semibold text-gray-900',
    3: 'text-xl font-semibold text-gray-900',
    4: 'text-lg font-medium text-gray-900',
    5: 'text-base font-medium text-gray-900',
    6: 'text-sm font-medium text-gray-900'
  };

  return React.createElement(
    HeadingTag,
    {
      id,
      className: `${defaultClasses[level]} ${className}`,
      ...props
    },
    children
  );
};

/**
 * Accessible Form Field Component
 */
export const AccessibleFormField = ({ 
  label, 
  id, 
  type = 'text',
  required = false,
  error,
  helpText,
  placeholder,
  value,
  onChange,
  className = '',
  ...props 
}) => {
  const errorId = error ? `${id}-error` : undefined;
  const helpId = helpText ? `${id}-help` : undefined;
  const describedBy = [errorId, helpId].filter(Boolean).join(' ') || undefined;

  return (
    <div className={`form-field ${className}`}>
      <label 
        htmlFor={id}
        className="block text-sm font-medium text-gray-700 mb-1"
      >
        {label}
        {required && <span className="text-red-500 ml-1" aria-label="required">*</span>}
      </label>
      
      <input
        type={type}
        id={id}
        required={required}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        aria-describedby={describedBy}
        aria-invalid={error ? 'true' : 'false'}
        className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
          error ? 'border-red-300' : 'border-gray-300'
        }`}
        {...props}
      />
      
      {helpText && (
        <p id={helpId} className="mt-1 text-sm text-gray-500">
          {helpText}
        </p>
      )}
      
      {error && (
        <p id={errorId} className="mt-1 text-sm text-red-600" role="alert">
          {error}
        </p>
      )}
    </div>
  );
};

/**
 * Accessible Modal Component
 */
export const AccessibleModal = ({ 
  isOpen, 
  onClose, 
  title, 
  children, 
  className = '',
  size = 'medium' 
}) => {
  const modalRef = React.useRef(null);
  const previousFocusRef = React.useRef(null);

  React.useEffect(() => {
    if (isOpen) {
      previousFocusRef.current = document.activeElement;
      modalRef.current?.focus();
    } else {
      previousFocusRef.current?.focus();
    }
  }, [isOpen]);

  React.useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const sizeClasses = {
    small: 'max-w-md',
    medium: 'max-w-lg',
    large: 'max-w-2xl',
    xlarge: 'max-w-4xl'
  };

  return (
    <div 
      className="fixed inset-0 z-50 overflow-y-auto"
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
    >
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
        aria-hidden="true"
      />
      
      {/* Modal */}
      <div className="flex min-h-screen items-center justify-center p-4">
        <div 
          ref={modalRef}
          tabIndex={-1}
          className={`relative bg-white rounded-lg shadow-xl w-full ${sizeClasses[size]} ${className}`}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 id="modal-title" className="text-lg font-semibold text-gray-900">
              {title}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md p-1"
              aria-label="Close modal"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          {/* Content */}
          <div className="p-6">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Accessible Loading Spinner
 */
export const AccessibleSpinner = ({ 
  size = 'medium', 
  className = '',
  label = 'Loading...' 
}) => {
  const sizeClasses = {
    small: 'w-4 h-4',
    medium: 'w-8 h-8',
    large: 'w-12 h-12'
  };

  return (
    <div 
      className={`inline-block animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ${sizeClasses[size]} ${className}`}
      role="status"
      aria-label={label}
    >
      <span className="sr-only">{label}</span>
    </div>
  );
};

/**
 * Screen Reader Only Text Component
 */
export const ScreenReaderOnly = ({ children, className = '' }) => {
  return (
    <span className={`sr-only ${className}`}>
      {children}
    </span>
  );
};

/**
 * Focus Trap Component for modals and dropdowns
 */
export const FocusTrap = ({ children, active = true }) => {
  const containerRef = React.useRef(null);

  React.useEffect(() => {
    if (!active) return;

    const container = containerRef.current;
    if (!container) return;

    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleTabKey = (e) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement?.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement?.focus();
          e.preventDefault();
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);
    return () => container.removeEventListener('keydown', handleTabKey);
  }, [active]);

  return (
    <div ref={containerRef}>
      {children}
    </div>
  );
};

export default {
  AccessibleImage,
  SkipToContent,
  AccessibleButton,
  AccessibleHeading,
  AccessibleFormField,
  AccessibleModal,
  AccessibleSpinner,
  ScreenReaderOnly,
  FocusTrap
};
