/**
 * SEO Optimization utilities for cross-linking
 */

import { DOMAINS, getCurrentSiteType } from './crossLinking';

/**
 * Generate structured data for app pages with cross-linking
 */
export const generateAppStructuredData = (appDetails, relatedAITools = []) => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": appDetails?.title || '',
    "applicationCategory": "MobileApplication",
    "operatingSystem": "Android",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    }
  };

  // Add app-specific properties
  if (appDetails?.description) {
    structuredData.description = appDetails.description;
  }

  if (appDetails?.icon) {
    structuredData.image = appDetails.icon;
  }

  if (appDetails?.appId) {
    structuredData.identifier = appDetails.appId;
  }

  // Add related AI tools as mentions for SEO
  if (relatedAITools.length > 0) {
    structuredData.mentions = relatedAITools.map(tool => ({
      "@type": "SoftwareApplication",
      "name": tool.name,
      "url": `${DOMAINS.AI_TOOLS}/tool/${tool.id}`,
      "applicationCategory": "WebApplication"
    }));
  }

  // Add cross-domain references for three-domain setup
  const currentSite = getCurrentSiteType();
  if (currentSite === 'all-versions' && appDetails?.appId) {
    // Reference latest version site
    structuredData.sameAs = [`${DOMAINS.APK_LATEST}/apps/appdetails/${appDetails.appId}`];
  } else if (currentSite === 'latest-only' && appDetails?.appId) {
    // Reference all versions site
    structuredData.sameAs = [`${DOMAINS.APK_ALL_VERSIONS}/apps/appdetails/${appDetails.appId}`];
  }

  return structuredData;
};

/**
 * Generate structured data for AI tool pages with cross-linking
 */
export const generateToolStructuredData = (toolDetails, relatedApps = []) => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": toolDetails?.title || '',
    "applicationCategory": "WebApplication",
    "url": toolDetails?.redirectLink || ''
  };

  // Add tool-specific properties
  if (toolDetails?.summary) {
    structuredData.description = toolDetails.summary;
  }

  if (toolDetails?.iconLocalPath) {
    structuredData.image = `${DOMAINS.AI_TOOLS}/${toolDetails.iconLocalPath}`;
  }

  if (toolDetails?.price) {
    structuredData.offers = {
      "@type": "Offer",
      "price": toolDetails.price === 'Free' ? "0" : "varies",
      "priceCurrency": "USD"
    };
  }

  // Add related mobile apps as mentions for SEO (both latest and all versions)
  if (relatedApps.length > 0) {
    structuredData.mentions = relatedApps.flatMap(app => [
      {
        "@type": "SoftwareApplication",
        "name": `${app.name} (Latest)`,
        "url": `${DOMAINS.APK_LATEST}/apps/appdetails/${app.id}`,
        "applicationCategory": "MobileApplication",
        "operatingSystem": "Android"
      },
      {
        "@type": "SoftwareApplication",
        "name": `${app.name} (All Versions)`,
        "url": `${DOMAINS.APK_ALL_VERSIONS}/apps/appdetails/${app.id}`,
        "applicationCategory": "MobileApplication",
        "operatingSystem": "Android"
      }
    ]);
  }

  return structuredData;
};

/**
 * Generate SEO-optimized meta tags for cross-linking
 */
export const generateCrossLinkMetaTags = (pageType, details, relatedItems = []) => {
  const metaTags = [];

  if (pageType === 'app') {
    const title = `${details?.title || 'App'} APK Download - Free Android App`;
    const description = `Download ${details?.title || 'this app'} APK for Android. ${relatedItems.length > 0 ? `Also discover related AI tools: ${relatedItems.slice(0, 3).map(item => item.name).join(', ')}.` : ''}`;
    
    metaTags.push(
      { name: 'title', content: title },
      { name: 'description', content: description },
      { property: 'og:title', content: title },
      { property: 'og:description', content: description },
      { property: 'og:type', content: 'website' },
      { name: 'twitter:title', content: title },
      { name: 'twitter:description', content: description },
      { name: 'twitter:card', content: 'summary_large_image' }
    );

    // Add canonical URL
    if (details?.appId) {
      const canonicalUrl = `${DOMAINS.APK_SITE}/apps/appdetails/${details.appId}`;
      metaTags.push({ rel: 'canonical', href: canonicalUrl });
    }

  } else if (pageType === 'tool') {
    const title = `${details?.title || 'AI Tool'} - AI Tool Review & Guide`;
    const description = `Discover ${details?.title || 'this AI tool'} features, pricing, and alternatives. ${relatedItems.length > 0 ? `Download related mobile apps: ${relatedItems.slice(0, 3).map(item => item.name).join(', ')}.` : ''}`;
    
    metaTags.push(
      { name: 'title', content: title },
      { name: 'description', content: description },
      { property: 'og:title', content: title },
      { property: 'og:description', content: description },
      { property: 'og:type', content: 'article' },
      { name: 'twitter:title', content: title },
      { name: 'twitter:description', content: description },
      { name: 'twitter:card', content: 'summary_large_image' }
    );

    // Add canonical URL
    if (details?.appId) {
      const canonicalUrl = `${DOMAINS.AI_TOOLS_SITE}/tool/${details.appId}`;
      metaTags.push({ rel: 'canonical', href: canonicalUrl });
    }
  }

  return metaTags;
};

/**
 * Generate internal linking suggestions for SEO
 */
export const generateInternalLinks = (pageType, details, relatedItems = []) => {
  const links = [];

  if (pageType === 'app' && relatedItems.length > 0) {
    // Add links to related AI tools
    relatedItems.slice(0, 5).forEach(tool => {
      links.push({
        url: `${DOMAINS.AI_TOOLS_SITE}/tool/${tool.id}`,
        anchor: `${tool.name} AI Tool`,
        title: `Discover ${tool.name} - AI tool related to ${details?.title || 'this app'}`,
        rel: 'noopener noreferrer'
      });
    });
  } else if (pageType === 'tool' && relatedItems.length > 0) {
    // Add links to related mobile apps
    relatedItems.slice(0, 5).forEach(app => {
      links.push({
        url: `${DOMAINS.APK_SITE}/apps/appdetails/${app.id}`,
        anchor: `Download ${app.name} APK`,
        title: `Download ${app.name} APK - Mobile app related to ${details?.title || 'this tool'}`,
        rel: 'noopener noreferrer'
      });
    });
  }

  return links;
};

/**
 * Generate breadcrumb structured data with cross-linking
 */
export const generateBreadcrumbData = (pageType, details, category = '') => {
  const breadcrumbs = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": []
  };

  if (pageType === 'app') {
    breadcrumbs.itemListElement = [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": DOMAINS.APK_SITE
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Android Apps",
        "item": `${DOMAINS.APK_SITE}/apps`
      }
    ];

    if (category) {
      breadcrumbs.itemListElement.push({
        "@type": "ListItem",
        "position": 3,
        "name": category,
        "item": `${DOMAINS.APK_SITE}/apps/${category.toLowerCase().replace(/\s+/g, '-')}`
      });
    }

    if (details?.title) {
      breadcrumbs.itemListElement.push({
        "@type": "ListItem",
        "position": breadcrumbs.itemListElement.length + 1,
        "name": details.title,
        "item": `${DOMAINS.APK_SITE}/apps/appdetails/${details.appId || ''}`
      });
    }

  } else if (pageType === 'tool') {
    breadcrumbs.itemListElement = [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": DOMAINS.AI_TOOLS_SITE
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "AI Tools",
        "item": `${DOMAINS.AI_TOOLS_SITE}/tool`
      }
    ];

    if (category) {
      breadcrumbs.itemListElement.push({
        "@type": "ListItem",
        "position": 3,
        "name": category,
        "item": `${DOMAINS.AI_TOOLS_SITE}/tools/${category.toLowerCase().replace(/\s+/g, '-')}`
      });
    }

    if (details?.title) {
      breadcrumbs.itemListElement.push({
        "@type": "ListItem",
        "position": breadcrumbs.itemListElement.length + 1,
        "name": details.title,
        "item": `${DOMAINS.AI_TOOLS_SITE}/tool/${details.appId || ''}`
      });
    }
  }

  return breadcrumbs;
};

/**
 * Generate FAQ structured data for cross-linking
 */
export const generateCrossLinkFAQ = (pageType, details) => {
  const faqData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": []
  };

  if (pageType === 'app') {
    faqData.mainEntity.push({
      "@type": "Question",
      "name": `What AI tools work well with ${details?.title || 'this app'}?`,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": `There are several AI tools that complement ${details?.title || 'this app'}. You can discover productivity, automation, and creative AI tools that enhance your mobile experience.`
      }
    });
  } else if (pageType === 'tool') {
    faqData.mainEntity.push({
      "@type": "Question",
      "name": `Is there a mobile app for ${details?.title || 'this tool'}?`,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": `You can find mobile apps with similar functionality to ${details?.title || 'this tool'}. Download APK files for Android apps that offer comparable features.`
      }
    });
  }

  return faqData;
};
