"use client";
import * as React from "react";
import {
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText, 
  Button,
  Typography,
  Box,
  Divider
} from "@mui/material";
import { styled } from "@mui/material/styles";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import BarChartIcon from '@mui/icons-material/BarChart';
import InsightsIcon from '@mui/icons-material/Insights';
import AppRegistrationIcon from '@mui/icons-material/AppRegistration';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialog-paper": {
    borderRadius: "12px",
    maxWidth: "600px",
    width: "100%",
  },
  "& .MuiDialogContent-root": {
    padding: theme.spacing(3),
  },
  "& .MuiDialogActions-root": {
    padding: theme.spacing(2),
  },
}));

const FeatureIcon = styled(ListItemIcon)({
  minWidth: "40px",
  color: "#00A3FF",
});

export default function CheckUserTypeModal({ open, onClose }) {
  const handleSignUp = () => {
    window.location.href = "/register";
  };

  return (
    <BootstrapDialog
      onClose={onClose}
      aria-labelledby="customized-dialog-title"
      open={open}
    >
      <DialogTitle
        sx={{ 
          m: 0, 
          p: 3, 
          background: "linear-gradient(135deg, #00A3FF 0%, #0085CC 100%)",
          fontWeight: 700,
          color: "white",
          letterSpacing: "0.5px",
          fontSize: "1.25rem"
        }}
        id="customized-dialog-title"
      >
        <Box display="flex" alignItems="center">
          <CheckCircleIcon sx={{ mr: 1.5 }} />
          Unlock Full Access to App Insights
        </Box>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: "absolute",
            right: 12,
            top: 12,
            color: "white"
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      
      <DialogContent dividers sx={{ pt: 3 }}>
        <Typography variant="body1" paragraph sx={{ mb: 3, fontSize: "1.05rem" }}>
          Discover the most comprehensive app analytics platform. Join AppsStoreSpy today to get:
        </Typography>
        
        <List disablePadding>
          <ListItem sx={{ py: 1.5, alignItems: 'flex-start' }}>
            <FeatureIcon>
              <TrendingUpIcon fontSize="small" />
            </FeatureIcon>
            <ListItemText 
              primary="Top App Rankings" 
              primaryTypographyProps={{ fontWeight: 600 }}
              secondary="See which apps are trending across different categories and countries with daily updates." 
            />
          </ListItem>
          
          <Divider variant="inset" component="li" />
          
          <ListItem sx={{ py: 1.5, alignItems: 'flex-start' }}>
            <FeatureIcon>
              <BarChartIcon fontSize="small" />
            </FeatureIcon>
            <ListItemText 
              primary="Detailed Analytics" 
              primaryTypographyProps={{ fontWeight: 600 }}
              secondary="Track downloads, revenue, ratings, and other key metrics with beautiful visualizations." 
            />
          </ListItem>
          
          <Divider variant="inset" component="li" />
          
          <ListItem sx={{ py: 1.5, alignItems: 'flex-start' }}>
            <FeatureIcon>
              <InsightsIcon fontSize="small" />
            </FeatureIcon>
            <ListItemText 
              primary="Market Trends" 
              primaryTypographyProps={{ fontWeight: 600 }}
              secondary="Stay ahead with predictive analytics and emerging market trend reports." 
            />
          </ListItem>
          
          <Divider variant="inset" component="li" />
          
          <ListItem sx={{ py: 1.5, alignItems: 'flex-start' }}>
            <FeatureIcon>
              <AppRegistrationIcon fontSize="small" />
            </FeatureIcon>
            <ListItemText 
              primary="Competitive Analysis" 
              primaryTypographyProps={{ fontWeight: 600 }}
              secondary="Compare multiple apps side-by-side with our powerful benchmarking tools." 
            />
          </ListItem>
        </List>
        
        <Typography variant="body2" sx={{ mt: 2, fontStyle: 'italic', color: 'text.secondary' }}>
          Join thousands of developers and marketers making data-driven decisions.
        </Typography>
      </DialogContent>
      
      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button 
          onClick={onClose} 
          variant="outlined" 
          sx={{
            mr: 2,
            px: 3,
            borderRadius: '8px',
            textTransform: 'none',
            fontWeight: 600
          }}
        >
          Maybe Later
        </Button>
        <Button
          autoFocus
          onClick={handleSignUp}
          variant="contained"
          sx={{
            px: 3,
            borderRadius: '8px',
            textTransform: 'none',
            fontWeight: 600,
            boxShadow: 'none',
            '&:hover': {
              boxShadow: '0 2px 8px rgba(0, 163, 255, 0.5)'
            }
          }}
        >
          Sign Up Free
        </Button>
      </DialogActions>
    </BootstrapDialog>
  );
}