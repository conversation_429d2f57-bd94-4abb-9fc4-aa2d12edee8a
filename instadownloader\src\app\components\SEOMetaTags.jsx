import Head from 'next/head';
import { generateToolStructuredData } from '../utils/seoOptimization';

/**
 * Comprehensive SEO Meta Tags Component for SmartTools
 * Includes Open Graph, Twitter Cards, and structured data
 */
const SEOMetaTags = ({
  title,
  description,
  keywords = [],
  image,
  url,
  type = 'website',
  toolDetails = null,
  relatedApps = [],
  author = 'SmartTools',
  siteName = 'SmartTools',
  locale = 'en_US',
  publishedTime = null,
  modifiedTime = null
}) => {
  // Generate structured data if tool details are provided
  const structuredData = toolDetails ? 
    generateToolStructuredData(toolDetails, relatedApps) : null;

  // Default values
  const defaultTitle = 'SmartTools | Discover Top AI Tools & Reviews | Your AI Resource';
  const defaultDescription = 'SmartTools is an AI tools platform featuring 1000+ tool reviews and value-packed blogs targeted for professionals to increase everyone\'s productivity and efficiency.';
  const defaultImage = '/images/smarttools-og.jpg';
  const defaultUrl = 'https://apk3.demo1.com';

  const finalTitle = title || defaultTitle;
  const finalDescription = description || defaultDescription;
  const finalImage = image || defaultImage;
  const finalUrl = url || defaultUrl;

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{finalTitle}</title>
      <meta name="description" content={finalDescription} />
      {keywords.length > 0 && (
        <meta name="keywords" content={keywords.join(', ')} />
      )}
      <meta name="author" content={author} />
      <meta name="robots" content="index, follow" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="language" content="English" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={finalUrl} />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={finalTitle} />
      <meta property="og:description" content={finalDescription} />
      <meta property="og:image" content={finalImage} />
      <meta property="og:url" content={finalUrl} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content={locale} />
      {publishedTime && (
        <meta property="article:published_time" content={publishedTime} />
      )}
      {modifiedTime && (
        <meta property="article:modified_time" content={modifiedTime} />
      )}
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={finalTitle} />
      <meta name="twitter:description" content={finalDescription} />
      <meta name="twitter:image" content={finalImage} />
      <meta name="twitter:url" content={finalUrl} />
      <meta name="twitter:site" content="@smarttools_ai" />
      <meta name="twitter:creator" content="@smarttools_ai" />
      
      {/* Additional Meta Tags for Better SEO */}
      <meta name="theme-color" content="#10B981" />
      <meta name="msapplication-TileColor" content="#10B981" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content={siteName} />
      
      {/* Favicon and Icons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />
      
      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData)
          }}
        />
      )}
      
      {/* Organization Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": siteName,
            "url": defaultUrl,
            "logo": `${defaultUrl}/st_logo.png`,
            "description": defaultDescription,
            "sameAs": [
              "https://twitter.com/smarttools_ai"
            ]
          })
        }}
      />
      
      {/* Website Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebSite",
            "name": siteName,
            "url": defaultUrl,
            "description": defaultDescription,
            "potentialAction": {
              "@type": "SearchAction",
              "target": {
                "@type": "EntryPoint",
                "urlTemplate": `${defaultUrl}/search?q={search_term_string}`
              },
              "query-input": "required name=search_term_string"
            }
          })
        }}
      />
    </Head>
  );
};

/**
 * Tool-specific SEO Meta Tags
 */
export const ToolSEOMetaTags = ({ toolDetails, relatedApps = [] }) => {
  if (!toolDetails) return null;

  const title = `${toolDetails.title} - AI Tool Review & Guide | SmartTools`;
  const description = `Discover ${toolDetails.title} features, pricing, and alternatives. ${toolDetails.description ? toolDetails.description.substring(0, 120) + '...' : 'Comprehensive AI tool review and guide.'} ${relatedApps.length > 0 ? `Download related mobile apps.` : ''}`;
  const keywords = [
    `${toolDetails.title}`,
    'AI tool',
    'artificial intelligence',
    toolDetails.category,
    'AI software',
    'productivity tool',
    'AI review',
    'tool comparison'
  ];
  const url = `https://apk3.demo1.com/tool/${toolDetails.id}`;
  const image = toolDetails.image || '/images/ai-tool-og.jpg';

  return (
    <SEOMetaTags
      title={title}
      description={description}
      keywords={keywords}
      image={image}
      url={url}
      type="article"
      toolDetails={toolDetails}
      relatedApps={relatedApps}
      publishedTime={toolDetails.publishedDate}
      modifiedTime={toolDetails.lastUpdated}
    />
  );
};

/**
 * Category-specific SEO Meta Tags for AI tool categories
 */
export const CategorySEOMetaTags = ({ category, tools = [] }) => {
  const title = `${category} AI Tools - Reviews & Comparisons | SmartTools`;
  const description = `Discover the best ${category} AI tools with comprehensive reviews and comparisons. Find the perfect AI solution for your ${category} needs.`;
  const keywords = [
    `${category} AI tools`,
    'AI software',
    'artificial intelligence',
    `${category} automation`,
    'AI reviews',
    'tool comparison',
    category.toLowerCase()
  ];
  const url = `https://apk3.demo1.com/tools/${category.toLowerCase()}`;

  return (
    <SEOMetaTags
      title={title}
      description={description}
      keywords={keywords}
      url={url}
      type="website"
    />
  );
};

export default SEOMetaTags;
