import "@fortawesome/fontawesome-svg-core/styles.css";
import { config } from "@fortawesome/fontawesome-svg-core";
import Script from "next/script";
config.autoAddCss = false;
import { Inter } from "next/font/google";
import "./globals.css";
import Navbar from "./Components/navbar/navbar";
import { ReduxProvider } from "./ReduxLayout/layout";
import Link from "next/link";
import UniversalNavigation from "./Components/UniversalNavigation";

const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  title: "APKExplorer - Fast Android APK Downloader",
  description:
    "APKExplorer is your go-to source for downloading Android APKs quickly and securely. Discover a vast library of apps, explore different versions, and stay updated with the latest releases.",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
        />
        <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8916641928046583"
          crossOrigin="anonymous" data-adtest="on"></script>
        <script src="https://cdn.jsdelivr.net/npm/@fingerprintjs/fingerprintjs@3/dist/fp.min.js"></script>
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-TW5T46HGBD"></script>
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-TW5T46HGBD');
          `}
        </Script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
                (function(c,l,a,r,i,t,y){
                    c[a] = c[a] || function () { (c[a].q = c[a].q || []).push(arguments) };
                    t=l.createElement(r);
                    t.async=1;
                    t.src="https://www.clarity.ms/tag/"+i;
                    y=l.getElementsByTagName(r)[0];
                    y.parentNode.insertBefore(t,y);
                })(window, document, "clarity", "script", "oa4v7ql0yx");`,
          }}
        />
      </head>
      <body className={inter.className}>
        <ReduxProvider>
          <Navbar />
          {children}
        </ReduxProvider>

        <footer className="bg-white dark:bg-gray-900">
          <div className="mx-auto w-full max-w-screen-xl p-4 py-6 lg:py-8">
            <div className="md:flex md:justify-between">
              <div className="w-full md:w-6/12 lg:w-6/12 mb-6 md:mb-0">
                <Link href="https://apk-mirror.com/" className="flex items-center">
                  <span className="self-center text-2xl font-semibold whitespace-nowrap dark:text-white">APKExplorer</span>
                </Link>
                <p className="lg:me-6 p-1 dark:text-gray-400">
                  APKExplorer is your go-to source for downloading Android APKs quickly and securely. Discover a vast library of apps, explore different versions.
                </p>
              </div>
              <div className="grid grid-cols-2 gap-8 sm:gap-6 sm:grid-cols-3">
                <div>
                  <h2 className="mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white">Pages</h2>
                  <ul className="text-gray-500 dark:text-gray-400 font-medium">
                    <li className="mb-4">
                      <Link href="/" className="hover:underline">Home</Link>
                    </li>
                    <li className="mb-4">
                      <Link href="/apps" className="hover:underline">Apps</Link>
                    </li>
                    <li className="mb-4">
                      <Link href="/games" className="hover:underline">Games</Link>
                    </li>
                  </ul>
                </div>
                <div>
                  <h2 className="mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white">Legal</h2>
                  <ul className="text-gray-500 dark:text-gray-400 font-medium">
                    <li className="mb-4">
                      <Link href="/privacy-policy" className="hover:underline">Privacy Policy</Link>
                    </li>
                    <li>
                      <Link href="terms-conditions" className="hover:underline">Terms &amp; Conditions</Link>
                    </li>
                  </ul>
                </div>
                {/* Universal Navigation for Cross-Site Links */}
                <UniversalNavigation variant="footer" />

                <div>
                  <h2 className="mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white">Explore More</h2>
                  <ul className="text-gray-500 dark:text-gray-400 font-medium">
                    <li className="mb-4">
                      <Link href="https://appsstorespy.io/" className="hover:underline">Apps</Link>
                    </li>
                    <li className="mb-4">
                      <Link href="https://instadownloader.app/" className="hover:underline">AI Tools</Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <hr className="my-6 border-gray-200 sm:mx-auto dark:border-gray-700 lg:my-8" />
            <div className="sm:flex sm:items-center sm:justify-between">
              <span className="text-sm text-gray-500 sm:text-center dark:text-gray-400">
                © 2024 <Link href="https://appsstorespy.io/" className="hover:underline">AppsStore</Link>. All Rights Reserved.
              </span>
              <div className="flex mt-4 sm:justify-center sm:mt-0">
                <Link href="#" className="text-gray-500 hover:text-gray-900 dark:hover:text-white">
                  <i className="fa-brands fa-facebook" />
                  <span className="sr-only">Facebook page</span>
                </Link>
                <Link href="#" className="text-gray-500 hover:text-gray-900 dark:hover:text-white ms-5">
                  <i className="fa-brands fa-discord" />
                  <span className="sr-only">Discord community</span>
                </Link>
                <Link href="#" className="text-gray-500 hover:text-gray-900 dark:hover:text-white ms-5">
                  <i className="fa-brands fa-twitter" />
                  <span className="sr-only">Twitter page</span>
                </Link>
                <Link href="#" className="text-gray-500 hover:text-gray-900 dark:hover:text-white ms-5">
                  <i className="fa-brands fa-github" />
                  <span className="sr-only">GitHub account</span>
                </Link>
                <Link href="#" className="text-gray-500 hover:text-gray-900 dark:hover:text-white ms-5">
                  <i className="fa-brands fa-dribbble" />
                  <span className="sr-only">Dribbble account</span>
                </Link>
              </div>
            </div>
          </div>
        </footer>
      </body>
    </html>
  );
}