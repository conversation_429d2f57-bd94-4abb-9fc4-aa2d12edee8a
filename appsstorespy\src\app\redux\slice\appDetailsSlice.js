import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

const initialState = {
  app: {
    appDetails:{},
    appRanks:[],
    analyticsData:[],
    user:{
      authorize: false
    }
  },
  loading: true,
  error: null,
};
export const fetchAppDetails = createAsyncThunk(
  "appInfo/getAppDetails",
  async (appId, { dispatch }) => {
    try {
      const response = await axios.get(`/api/app_by_id?appId=${appId}`);
      return response.data.app;
    } catch (error) {
      dispatch(setError(error.message));
      throw error;
    }
  }
);


const appSlice = createSlice({
  name: "appInfo",
  initialState,
  reducers: {
    setAppData: (state, action) => {
      state.app = action.payload;
      state.loading = false;
      state.error = null;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setError: (state, action) => {
      state.loading = false;
      state.app = {};
      state.error = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchAppDetails.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAppDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.app = action.payload;
        if (state.app.user) {
          state.app.user.authorize = action.payload.authorize;
        }
      })

      .addCase(fetchAppDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
  },
});

export const { setAppData, setLoading, setError } = appSlice.actions;

export default appSlice.reducer;
