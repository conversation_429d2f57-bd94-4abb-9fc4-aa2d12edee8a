
import axios from 'axios';
import { Box, Typography, Button } from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import RenderApps from './RenderComponents';

const getAppDetails = async (appId) => {
  try {
    const response = await axios.get(`${process.env.NEXTAUTH_URL}/api/app_by_id?appId=${appId}`);
    if (response.status === 200) {
      const appData = response.data.app;

      const obfuscatedAppDetails = {
        appId: appData.appDetails.appId,
        adSupported: appData.appDetails.adSupported,
        title: appData.appDetails.title,
        showTitle: appData.appDetails.title,
        summary: appData.appDetails.summary,
        url: appData.appDetails.url,
        updated: appData.appDetails.updated,
        video: appData.appDetails.video,
        version: appData.appDetails.version,
        contentRating: appData.appDetails.contentRating,
        description: appData.appDetails.description,
        developer: appData.appDetails.developer,
        developerId:appData.appDetails.developerId,
        showDeveloper: appData.appDetails.developer,
        free: appData.appDetails.free,
        genre: appData.appDetails.genre,
        headerImage: appData.appDetails.headerImage,
        histogram: appData.appDetails.histogram,
        icon: appData.appDetails.icon,
        linearRatings: appData.appDetails.linearRatings,
        maxInstalls: appData.appDetails.maxInstalls,
        offersIAP: appData.appDetails.offersIAP,
        ratings: appData.appDetails.ratings,
        reviews: appData.appDetails.reviews,
        recentChanges: appData.appDetails.recentChanges,
        released: appData.appDetails.released,
        scoreText: appData.appDetails.scoreText,
        score: appData.appDetails.score,
        screenshots: appData.appDetails.screenshots,
      };
      const appRanks = appData.appRanks;
      const obfuscatedAnalyticsData = appData.analyticsData?.map((app) => {
        const formattedMonthlyTotal = {
          installs:app.monthlyTotal.installs,
          ratings: app.monthlyTotal.ratings,
          reviews:app.monthlyTotal.reviews,
          dailyInstalls: app.monthlyTotal.dailyInstalls
        };
        return {
          appId: app.appId,
          analytics: app.analytics,
          monthlyTotal: formattedMonthlyTotal
        }
      });
      const mergeData = {
        appDetails: obfuscatedAppDetails,
        appRanks: appRanks,
        analyticsData: obfuscatedAnalyticsData
      }
      return {
        app: mergeData,
      };
    } else if (response.status === 404) {
      console.error("App information not found");
      return { app: null };
    } else {
      console.error("Unexpected status code:", response.status);
      return { app: null };
    }
  } catch (error) {
    console.error("Error fetching app details:", error);
    return { app: null };
  }
};

export default async function AppDetails({ params: { appId } }) {
  const appDetails = await getAppDetails(appId);
   const appName = `Android Apps by ${appDetails?.app?.appDetails?.showTitle} on Google Play`;
  return (
    <>
      {appDetails.app ? (
        <>
          <RenderApps appDetailsInfo={appDetails} />
          <metadata>
            <title>{appName}</title>
          </metadata>
        </>
      ) : (
        <><metadata>
          <title>Not Found</title>
        </metadata>
         <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '80vh',
            textAlign: 'center',
            p: 3
          }}>
            <Box sx={{
              bgcolor: 'background.paper',
              borderRadius: 4,
              p: 4,
              boxShadow: 3,
              maxWidth: '500px',
              width: '100%'
            }}>
              <ErrorOutlineIcon
                sx={{
                  fontSize: 80,
                  color: 'error.main',
                  mb: 2
                }}
              />
              <Typography variant="h5" component="h1" gutterBottom sx={{ fontWeight: 700 }}>
                App Not Found
              </Typography>
              <Typography variant="body1" color="text.secondary" paragraph>
                We couldn't find any information for this App.
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
               The app ID may be incorrect or the app may have been removed.
              </Typography>
              <Button
                variant="contained"
                href="/"
                sx={{
                  backgroundColor: '#00A3FF',
                  '&:hover': { backgroundColor: '#0088cc',color:"white" },
                  mt: 3
                }}
              >
                Back to Home
              </Button>
            </Box>
          </Box>
          </>

      )}
    </>
  );
}
