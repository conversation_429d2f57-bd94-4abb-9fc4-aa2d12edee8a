import Image from "next/image";
import Link from "next/link";


const Card = ({ tools, searchToggle = false, grid = 2, errorMsg = '' }) => {


    return (
        <div className={`grid ${tools?.length === 1 || tools?.length === 0 ? 'grid-cols-1' : `grid-cols-1 md:grid-cols-2 xl:grid-cols-${grid}`} gap-4 p-4`}>
        {tools?.length > 0 ? (
            tools.map((app, index) => (
                <div
                    key={app.appId}
                    className="hover:bg-gray-100 p-4 shadow-sm border rounded-md border-gray-200"
                >
                    <Link href={`/tool/${app.appId}`} target="_blank" prefetch={false}>
                        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                            <Image
                                className="rounded-2xl w-full h-auto sm:w-20 sm:h-20"
                                width={75}
                                height={75}
                                src={`/${app.iconLocalPath}`}
                                alt={`${app.title} Icon`}
                                priority={true}
                            />
                            <div className="mt-4 sm:mt-0 sm:ml-4 flex-1">
                                <div className="flex justify-between items-start">
                                    <h4 className="text-sm sm:text-base font-medium">{app.title}</h4>
                                    <p className="px-3 py-1 text-xs sm:text-sm cursor-text font-semibold text-white bg-gradient-to-r from-green-400 to-blue-500 rounded-full border border-transparent shadow-lg dark:bg-gradient-to-r dark:from-green-500 dark:to-blue-600">
                                        {app.price}
                                    </p>
                                </div>
                                <p className="text-xs text-slate-400 tracking-wider mt-1">
                                    {app.category}
                                </p>
                                <p className="text-xs sm:text-sm text-slate-700 tracking-wider mt-2">
                                    {app.summary}
                                </p>
                                <div className="flex flex-wrap gap-2 mt-3">
                                    {app.tags?.map((tag, index) => (
                                        <Link
                                            key={index}
                                            href={tag.tagLink}
                                            className="px-2 py-1 text-xs sm:text-sm text-gray-600 bg-gray-200 rounded-full hover:bg-gray-300 transition duration-300"
                                        >
                                            {tag.tagName}
                                        </Link>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </Link>
                </div>
            ))
        ) : (
            <div className="flex items-center justify-center w-full p-4 text-center">
                {searchToggle ? <p className="text-gray-600">{errorMsg}</p> : <p className="text-gray-600">{errorMsg}</p>}
            </div>
        )}
    </div>
    
    );
};

export default Card;

