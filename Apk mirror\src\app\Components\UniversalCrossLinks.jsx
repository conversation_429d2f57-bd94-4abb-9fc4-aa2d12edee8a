"use client";
import React from 'react';
import Link from 'next/link';
import { 
  generateAIToolsURL, 
  generateLatestVersionURL,
  generateOlderVersionsURL,
  generateAnchorText, 
  trackCrossLinkClick,
  isCrossLinkingEnabled,
  getCurrentSiteType
} from '../utils/crossLinking';

/**
 * Universal Cross-Link Button Component
 * Automatically shows appropriate buttons based on current site type
 */
const UniversalCrossLinks = ({ 
  appDetails, 
  variant = 'horizontal',
  size = 'medium',
  className = '',
  showIcons = true 
}) => {
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const currentSite = getCurrentSiteType();
  const buttons = [];

  // Generate appropriate buttons based on current site
  if (currentSite === 'all-versions') {
    // On apkdemo1.com (all versions site)
    
    // 1. "Get Latest Version" button → apk2demo1.com
    const latestURL = generateLatestVersionURL(appDetails);
    if (latestURL) {
      buttons.push({
        type: 'latest-version',
        url: latestURL,
        text: 'Get Latest Version',
        icon: '🔄',
        color: 'bg-green-600 hover:bg-green-700',
        description: `Get the latest version of ${appDetails?.title || 'this app'}`
      });
    }

    // 2. "Discover AI Tools" button → apk3.demo1.com
    const aiToolsURL = generateAIToolsURL(appDetails);
    if (aiToolsURL) {
      buttons.push({
        type: 'ai-tools',
        url: aiToolsURL,
        text: generateAnchorText('ai-tools', appDetails),
        icon: '⚡',
        color: 'bg-blue-600 hover:bg-blue-700',
        description: `Discover AI tools related to ${appDetails?.title || 'this app'}`
      });
    }

  } else if (currentSite === 'latest-only') {
    // On apk2demo1.com (latest versions site)
    
    // 1. "Get Older Versions" button → apkdemo1.com
    const olderVersionsURL = generateOlderVersionsURL(appDetails);
    if (olderVersionsURL) {
      buttons.push({
        type: 'older-versions',
        url: olderVersionsURL,
        text: 'Get Older Versions',
        icon: '📦',
        color: 'bg-purple-600 hover:bg-purple-700',
        description: `Download older versions of ${appDetails?.title || 'this app'}`
      });
    }

    // 2. "Explore AI Tools" button → apk3.demo1.com
    const aiToolsURL = generateAIToolsURL(appDetails);
    if (aiToolsURL) {
      buttons.push({
        type: 'ai-tools',
        url: aiToolsURL,
        text: 'Explore AI Tools',
        icon: '🤖',
        color: 'bg-blue-600 hover:bg-blue-700',
        description: `Explore AI tools related to ${appDetails?.title || 'this app'}`
      });
    }
  }

  if (buttons.length === 0) {
    return null;
  }

  // Style variants
  const sizes = {
    small: 'px-3 py-2 text-sm',
    medium: 'px-6 py-3 text-base',
    large: 'px-8 py-4 text-lg'
  };

  const containerClasses = variant === 'vertical' 
    ? 'flex flex-col gap-3' 
    : 'flex flex-col sm:flex-row gap-3 justify-center items-center';

  const handleClick = (button) => {
    trackCrossLinkClick(currentSite, button.type, appDetails);
  };

  return (
    <div className={`${containerClasses} ${className}`}>
      {buttons.map((button, index) => (
        <Link
          key={index}
          href={button.url}
          onClick={() => handleClick(button)}
          target="_blank"
          rel="noopener noreferrer"
          className={`
            inline-flex items-center justify-center gap-2 
            font-medium rounded-lg transition-all duration-200 
            focus:outline-none focus:ring-2 focus:ring-offset-2
            transform hover:scale-105 active:scale-95 text-white
            ${button.color} 
            ${sizes[size]}
          `}
          title={button.description}
        >
          {showIcons && <span className="text-lg">{button.icon}</span>}
          <span>{button.text}</span>
          <svg 
            className="w-4 h-4" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" 
            />
          </svg>
        </Link>
      ))}
    </div>
  );
};

/**
 * Compact Cross-Links for sidebars or smaller spaces
 */
export const CompactCrossLinks = ({ appDetails, className = '' }) => {
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const currentSite = getCurrentSiteType();
  const links = [];

  if (currentSite === 'all-versions') {
    const latestURL = generateLatestVersionURL(appDetails);
    const aiToolsURL = generateAIToolsURL(appDetails);
    
    if (latestURL) {
      links.push({ url: latestURL, text: 'Latest Version', type: 'latest-version' });
    }
    if (aiToolsURL) {
      links.push({ url: aiToolsURL, text: 'AI Tools', type: 'ai-tools' });
    }
  } else if (currentSite === 'latest-only') {
    const olderVersionsURL = generateOlderVersionsURL(appDetails);
    const aiToolsURL = generateAIToolsURL(appDetails);
    
    if (olderVersionsURL) {
      links.push({ url: olderVersionsURL, text: 'Older Versions', type: 'older-versions' });
    }
    if (aiToolsURL) {
      links.push({ url: aiToolsURL, text: 'AI Tools', type: 'ai-tools' });
    }
  }

  if (links.length === 0) {
    return null;
  }

  const handleClick = (link) => {
    trackCrossLinkClick(currentSite, link.type, appDetails);
  };

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {links.map((link, index) => (
        <Link
          key={index}
          href={link.url}
          onClick={() => handleClick(link)}
          target="_blank"
          rel="noopener noreferrer"
          className="
            inline-flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800 
            hover:underline transition-colors duration-200 px-2 py-1 rounded
            bg-blue-50 hover:bg-blue-100
          "
        >
          {link.text}
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
          </svg>
        </Link>
      ))}
    </div>
  );
};

/**
 * Banner Cross-Links for prominent placement
 */
export const BannerCrossLinks = ({ appDetails, className = '' }) => {
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const currentSite = getCurrentSiteType();
  let bannerConfig = null;

  if (currentSite === 'all-versions') {
    bannerConfig = {
      title: 'Get the Latest Version',
      description: `Download the newest version of ${appDetails?.title || 'this app'} or discover related AI tools`,
      gradient: 'from-green-50 to-blue-50',
      border: 'border-green-200'
    };
  } else if (currentSite === 'latest-only') {
    bannerConfig = {
      title: 'Explore More Options',
      description: `Get older versions of ${appDetails?.title || 'this app'} or explore AI tools`,
      gradient: 'from-purple-50 to-blue-50',
      border: 'border-purple-200'
    };
  }

  if (!bannerConfig) {
    return null;
  }

  return (
    <div className={`bg-gradient-to-r ${bannerConfig.gradient} border ${bannerConfig.border} rounded-lg p-4 ${className}`}>
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            {bannerConfig.title}
          </h3>
          <p className="text-gray-600 text-sm">
            {bannerConfig.description}
          </p>
        </div>
        <UniversalCrossLinks 
          appDetails={appDetails} 
          variant="horizontal" 
          size="medium"
        />
      </div>
    </div>
  );
};

export default UniversalCrossLinks;
