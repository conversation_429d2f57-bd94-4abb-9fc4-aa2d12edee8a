import connectDB from "@/app/database/mongoose";

import NextAuth from "next-auth/next";
import CredentialsProvider from "next-auth/providers/credentials";
import bcrypt from "bcryptjs";
import User from "@/app/database/user";

const login = async (credentials) => {
  try {
    await connectDB();
    const user = await User.findOne({ email: credentials.email });
    if (!user) throw new Error("Wrong Credentials");
    const isPassCorrect = await bcrypt.compare(
      credentials.password,
      user.password
    );
    if (!isPassCorrect) throw new Error("Wrong Credentials");
    return user;
  } catch (error) {
    throw new Error("Something went wrong");
  }
};

export const authOptions = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {},
      async authorize(credentials, req) {
        try {
          await connectDB();
          const user = await login(credentials);
          return user;
        } catch (e) {
          throw new Error("Failed to login");
        }
      },
    }),
  ],
  secret: process.env.NEXTAUTH_SECRET,
  session: {
    strategy: "jwt",
  },
  pages: {
    signIn: "/login",
  },
  callbacks: {
    async jwt({token, user}) {
      if (user) {
        token.email = user.email;
        token._id = user._id;
      }
      return token;
    },
    async session({session, token}) {
      if (token) {
        session.user.email = token.email;
        session.user._id = token._id;
      }
      return session;
    },
  },
};

const handler = NextAuth(authOptions);
export { handler as GET, handler as POST };