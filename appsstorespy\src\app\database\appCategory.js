"use strict";

import mongoose from "mongoose";

const Schema = mongoose.Schema;

const appSchema = new Schema({
  appId: { type: String, required: true},
  countryCode: { type: String, required: true },
  collectionName: { type: String, required: true },
  category: {type:String, required:true},
  position: { type: Number, required: true },
},{ timestamps: true });
appSchema.index({ appId: 1, countryCode: 1, collectionName: 1, category: 1 }, { unique: true });

mongoose.models = {};
export default mongoose.model("ApplicationCategory", appSchema);