"use client";
import React, { useEffect, useState, useRef } from "react";
import Link from "next/link";
import Image from "next/image";
import SideBar from "@/app/components/Sidebar";
import { categories, recentlyUpdatedTools } from "@/app/util/constants";
import DualAPKLinks from "@/app/components/DualAPKLinks";

const ToolDetails = ({ appDetails }) => {
  const [activeFaqIndex, setActiveFaqIndex] = useState(null);
  const [activeIndex, setActiveIndex] = useState(0);
  const [contentHeight, setContentHeight] = useState("90px");
  const [isExpanded, setIsExpanded] = useState(false);
  const contentRef = useRef(null);
  const MAX_LENGTH = 900;



  useEffect(() => {
    if (contentRef.current) {
      setContentHeight(isExpanded ? `${contentRef.current.scrollHeight}px` : "90px");
    }
  }, [isExpanded]);

  const getNameFromCategory = (category) => {
    const foundCategory = categories.find(item => item.name === category);
    return foundCategory ? foundCategory.category : null;
  };
  const categoryName = getNameFromCategory(appDetails?.toolDetails.subCategory);

  const handlePrev = () => {
    setActiveIndex((prevIndex) =>
      prevIndex === 0 ? appDetails?.toolDetails.screenShots.length - 1 : prevIndex - 1
    );
  };

  const handleNext = () => {
    setActiveIndex((prevIndex) =>
      prevIndex === appDetails?.toolDetails.screenShots.length - 1 ? 0 : prevIndex + 1
    );
  };

  const handleToggle = (index) => {
    setActiveFaqIndex(index === activeFaqIndex ? null : index);
  };
  const toggleContent = () => {
    setIsExpanded(!isExpanded);
  };

  const contentLength = appDetails?.toolDetails?.overview.length;
  const shouldShowLess = contentLength > MAX_LENGTH;

  return (
    <>
            <metadata>
              <title>{appDetails?.toolDetails.title}</title>
            </metadata>
            <main className="lg:container flex flex-col items-center justify-between mt-0.5 mx-5 sm:mx-0 md:mx-20 lg:mx-auto">
              <div className="lg:container flex flex-col items-center justify-between mt-0.5 mx-5 sm:mx-0 md:mx-20 lg:mx-auto">
                <div className="container mx-auto max-w-screen-xl">
                  <div className="w-full justify-center flex flex-wrap">
                    <main className="w-full xl:w-4/6 mt-4 relative px-0 xl:px-2">
                      <div className="mb-3.5 p-3 bg-white rounded-md shadow-md">
                        <p className="text-[10px] sm:text-sm">
                          <Link href={"/"} prefetch={false}>Home</Link>&nbsp;/&nbsp;
                          <Link href={`/tools/${categoryName}`} prefetch={false}>{appDetails?.toolDetails.subCategory}</Link>&nbsp;/ <span className="text-slate-500">{appDetails?.toolDetails.title}</span>
                        </p>

                      </div>
                      <div className="mb-3.5 p-5 bg-white rounded-lg shadow-md flex flex-col">
                        <h2 className="mb-2.5 text-base font-normal text-slate-500 uppercase tracking-wider">
                          AI {categoryName}
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-1 xl:grid-cols-1 gap-4 p-4">
                          <div key={appDetails?.toolDetails.appId}>
                            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                              <Image
                                className="rounded-3xl"
                                src={`/${appDetails?.toolDetails.iconLocalPath}`}
                                alt={`${appDetails?.toolDetails.title} icon`}
                                width={150}
                                height={150}
                                priority={true}
                              />

                              <div className="mt-4 sm:mt-0 sm:ml-4 flex-1">
                                <div className="flex justify-between items-start">
                                  <h4 className="text-2xl sm:text-xl font-medium">{appDetails?.toolDetails.title}</h4>
                                  <p className="px-3 py-1 text-xs sm:text-sm cursor-text font-semibold text-white bg-gradient-to-r from-green-400 to-blue-500 rounded-full border border-transparent shadow-lg dark:bg-gradient-to-r dark:from-green-500 dark:to-blue-600">
                                    {appDetails?.toolDetails.price}
                                  </p>
                                </div>
                                <p className="text-xs text-slate-400 tracking-wider mt-1">
                                  {appDetails?.toolDetails.category}
                                </p>
                                <p className="text-xs sm:text-sm text-slate-700 tracking-wider mt-2">
                                  {appDetails?.toolDetails.summary}
                                </p>

                                <div className="flex flex-wrap gap-2 mt-3">
                                  {appDetails?.toolDetails.tags?.map((tag, index) => (
                                    <Link
                                      key={index}
                                      href={tag.tagLink}
                                      className="px-2 py-1 text-xs sm:text-sm text-gray-600 bg-gray-200 rounded-full hover:bg-gray-300  transition duration-300"
                                    >
                                      {tag.tagName}
                                    </Link>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>

                        </div>

                        <div className="-mx-5 px-1.5 py-2 rounded-b-md">
                          <div className="px-4 flex flex-col sm:flex-row gap-3 justify-center items-center">
                            <Link
                              href={appDetails?.toolDetails.redirectLink}
                              target="_blank"
                              className="px-6 py-3 flex items-center justify-between gap-2 text-xs sm:text-sm md:text-base bg-slate-900 text-white uppercase rounded-lg hover:bg-slate-700"
                              prefetch={false}
                            >
                              Visit Website <i className="fa-solid fa-arrow-up-right-from-square" />
                            </Link>

                            {/* Dual APK Download Links */}
                            <DualAPKLinks
                              toolDetails={appDetails?.toolDetails}
                              variant="horizontal"
                              size="medium"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="my-5 p-5 bg-white rounded-md shadow-md">
                        <h2 className="mb-2.5 text-base font-normal text-slate-500 uppercase tracking-wider">
                          {appDetails?.toolDetails.title} screenShots
                        </h2>
                        <div id="indicators-carousel" className="relative w-full">
                          <div className="relative h-56 overflow-hidden rounded-lg md:h-96">
                            <div className="flex transition-transform duration-700 ease-in-out" style={{ transform: `translateX(-${activeIndex * 100}%)` }}>
                              {appDetails?.toolDetails.screenShots.map((img, index) => (
                                <div
                                  key={index}
                                  className="w-full flex-shrink-0"
                                  data-carousel-item={index === activeIndex ? "active" : ""}
                                >
                                  <img
                                   src={`/${img}`}
                                    className="block w-full"
                                    alt={`Slide ${index + 1}`}
                                  />
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* Indicator buttons */}
                          <div className="absolute z-5 flex -translate-x-1/2 space-x-3 bottom-5 left-1/2">
                            {appDetails?.toolDetails.screenShots.map((_, index) => (
                              <button
                                key={index}
                                onClick={() => setActiveIndex(index)}
                                className={`w-3 h-3 rounded-full ${index === activeIndex ? "bg-slate-900" : "bg-slate-400"
                                  }`}
                                aria-label={`Slide ${index + 1}`}
                              ></button>
                            ))}
                          </div>

                          {/* Previous button */}
                          <button
                            onClick={handlePrev}
                            className="absolute top-0 left-0 z-5 flex items-center justify-center h-full px-4 cursor-pointer group focus:outline-none"
                            data-carousel-prev
                          >
                            <span className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-slate-900 dark:bg-gray-800/30 group-hover:bg-slate-700 dark:group-hover:bg-gray-800/60 group-focus:ring-4 group-focus:ring-white dark:group-focus:ring-gray-800/70 group-focus:outline-none">
                              <i className="fa-solid text-white fa-chevron-left" />
                            </span>
                          </button>

                          {/* Next button */}
                          <button
                            onClick={handleNext}
                            className="absolute top-0 right-0 z-5 flex items-center justify-center h-full px-4 cursor-pointer group focus:outline-none"
                            data-carousel-next
                          >
                            <span className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-slate-900 dark:bg-gray-800/30 group-hover:bg-slate-700 dark:group-hover:bg-gray-800/60 group-focus:ring-4 group-focus:ring-white dark:group-focus:ring-gray-800/70 group-focus:outline-none">
                              <i className="fa-solid text-white fa-chevron-right" />
                            </span>
                          </button>
                        </div>
                      </div>
                      <div className="my-5 p-5 bg-white rounded-md shadow-md">
                        <h2 className="mb-2.5 text-base font-normal text-slate-500 uppercase tracking-wider">
                          Overview of {appDetails?.toolDetails.title}
                        </h2>

                        <div
                          ref={contentRef}
                          style={{
                            maxHeight: contentHeight,
                            overflow: "hidden",
                          }}
                          className="transition-max-height duration-500 ease-in-out"
                        >
                          <div
                            dangerouslySetInnerHTML={{ __html: appDetails?.toolDetails.overview }}
                          ></div>
                        </div>

                        {shouldShowLess && (
                          <button
                            onClick={toggleContent}
                            className="flex justify-center align-center w-full text-white font-medium text-sm mt-2"
                          >
                            <span className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-slate-900 hover:bg-slate-600 dark:bg-gray-800/30 group-hover:bg-slate-600 dark:group-hover:bg-gray-800/60 group-focus:ring-4 group-focus:ring-white dark:group-focus:ring-gray-800/70 group-focus:outline-none">
                              {isExpanded ? (
                                <i className="fa-solid fa-angle-up" />
                              ) : (
                                <i className="fa-solid fa-angle-down" />
                              )}
                            </span>
                          </button>
                        )}
                      </div>
                      <div className="my-5 p-5 bg-white rounded-md shadow-md">
                        <h2 className="mb-2.5 text-base font-normal text-slate-500 uppercase tracking-wider">
                          FAQs Of {appDetails?.toolDetails.title}
                        </h2>

                        <div
                          id="accordion-flush"
                          className="space-y-4"
                          data-accordion="collapse"
                          data-active-classes="bg-white dark:bg-gray-900 text-gray-900 dark:text-white"
                          data-inactive-classes="text-gray-500 dark:text-gray-400"
                        >
                          {appDetails?.toolDetails.faq.map((faq, index) => (
                            <div key={index} className="p-2 rounded-xl bg-slate-100 hover:bg-slate-200">
                              <h2 id={`accordion-flush-heading-${index}`}>
                                <button
                                  type="button"
                                  className="flex items-center justify-between w-full py-2 font-medium border-gray-200 dark:border-gray-700 dark:text-gray-400 gap-3"
                                  data-accordion-target={`#accordion-flush-body-${index}`}
                                  aria-expanded={activeFaqIndex === index}
                                  aria-controls={`accordion-flush-body-${index}`}
                                  onClick={() => handleToggle(index)}
                                >
                                  <span>{faq.question}</span>
                                  {activeFaqIndex === index ? (
                                    <i className="fa-solid fa-angle-up" />
                                  ) : (
                                    <i className="fa-solid fa-angle-down" />
                                  )}
                                </button>
                              </h2>
                              <div
                                id={`accordion-flush-body-${index}`}
                                className={`transition-[max-height] duration-500 ease-in-out overflow-hidden ${activeFaqIndex === index ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
                                  }`}
                                aria-labelledby={`accordion-flush-heading-${index}`}
                              >
                                <div className="py-5 border-gray-200 dark:border-gray-700">
                                  <p className="text-slate-700 dark:text-gray-400">{faq.answer}</p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>


                    </main>
                    <aside className=" sidebar-container w-full xl:w-2/6 xl:px-2">
                      <SideBar sideToolsDetails={appDetails?.similarTools} isLoading={false}  header="SIMILAR TOOLS" />
                      <SideBar sideToolsDetails={recentlyUpdatedTools}  isLoading={false} header="RECENTLY UPDATED TOOLS" />
                    </aside>
                  </div>
                </div>
              </div>
            </main>
    </>
  );
};

export default ToolDetails;



