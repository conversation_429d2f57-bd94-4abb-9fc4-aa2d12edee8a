"use client";
import React, { Suspense, lazy } from 'react';
import Image from 'next/image';
import { Box, Skeleton } from '@mui/material';

/**
 * Optimized Image Component with Next.js Image optimization
 */
export const OptimizedImage = ({ 
  src, 
  alt, 
  width, 
  height, 
  priority = false,
  quality = 75,
  placeholder = 'blur',
  blurDataURL,
  className = '',
  sizes,
  fill = false,
  style = {},
  onLoad,
  onError,
  ...props 
}) => {
  // Generate a simple blur placeholder if none provided
  const defaultBlurDataURL = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==';

  return (
    <Box className={className} style={style}>
      <Image
        src={src}
        alt={alt}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        priority={priority}
        quality={quality}
        placeholder={placeholder}
        blurDataURL={blurDataURL || defaultBlurDataURL}
        sizes={sizes}
        onLoad={onLoad}
        onError={onError}
        style={{
          objectFit: 'cover',
          ...style
        }}
        {...props}
      />
    </Box>
  );
};

/**
 * Lazy Loading Component with Intersection Observer
 */
export const LazyLoad = ({ 
  children, 
  height = 200, 
  offset = 100,
  placeholder = null,
  className = '' 
}) => {
  const [isVisible, setIsVisible] = React.useState(false);
  const [hasLoaded, setHasLoaded] = React.useState(false);
  const elementRef = React.useRef(null);

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasLoaded) {
          setIsVisible(true);
          setHasLoaded(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: `${offset}px`,
        threshold: 0.1
      }
    );

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => observer.disconnect();
  }, [offset, hasLoaded]);

  return (
    <div 
      ref={elementRef} 
      className={className}
      style={{ minHeight: height }}
    >
      {isVisible ? children : (placeholder || <Skeleton height={height} />)}
    </div>
  );
};

/**
 * Code Splitting HOC for lazy loading components
 */
export const withLazyLoading = (importFunc, fallback = <Skeleton height={200} />) => {
  const LazyComponent = lazy(importFunc);
  
  return (props) => (
    <Suspense fallback={fallback}>
      <LazyComponent {...props} />
    </Suspense>
  );
};

/**
 * Preload Component for critical resources
 */
export const PreloadResources = ({ resources = [] }) => {
  React.useEffect(() => {
    resources.forEach(resource => {
      const link = document.createElement('link');
      link.rel = resource.rel || 'preload';
      link.href = resource.href;
      link.as = resource.as;
      if (resource.type) link.type = resource.type;
      if (resource.crossOrigin) link.crossOrigin = resource.crossOrigin;
      document.head.appendChild(link);
    });
  }, [resources]);

  return null;
};

/**
 * Virtual Scrolling Component for large lists
 */
export const VirtualScrollList = ({ 
  items, 
  itemHeight, 
  containerHeight, 
  renderItem,
  overscan = 5,
  className = '' 
}) => {
  const [scrollTop, setScrollTop] = React.useState(0);
  const containerRef = React.useRef(null);

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );

  const visibleItems = items.slice(startIndex, endIndex + 1);
  const totalHeight = items.length * itemHeight;
  const offsetY = startIndex * itemHeight;

  const handleScroll = (e) => {
    setScrollTop(e.target.scrollTop);
  };

  return (
    <div
      ref={containerRef}
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((item, index) => (
            <div key={startIndex + index} style={{ height: itemHeight }}>
              {renderItem(item, startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

/**
 * Debounced Input Component
 */
export const DebouncedInput = ({ 
  value, 
  onChange, 
  delay = 300, 
  ...props 
}) => {
  const [localValue, setLocalValue] = React.useState(value);

  React.useEffect(() => {
    setLocalValue(value);
  }, [value]);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      onChange(localValue);
    }, delay);

    return () => clearTimeout(timer);
  }, [localValue, delay, onChange]);

  return (
    <input
      {...props}
      value={localValue}
      onChange={(e) => setLocalValue(e.target.value)}
    />
  );
};

/**
 * Memoized Component Wrapper
 */
export const MemoizedComponent = React.memo(({ children, dependencies = [] }) => {
  return React.useMemo(() => children, dependencies);
});

/**
 * Performance Monitor Hook
 */
export const usePerformanceMonitor = (name) => {
  React.useEffect(() => {
    if (typeof window !== 'undefined' && window.performance) {
      const startTime = performance.now();
      
      return () => {
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        // Log performance metrics
        console.log(`${name} render time: ${duration.toFixed(2)}ms`);
        
        // Send to analytics if available
        if (window.gtag) {
          window.gtag('event', 'timing_complete', {
            name: name,
            value: Math.round(duration)
          });
        }
      };
    }
  });
};

/**
 * Resource Hints Component
 */
export const ResourceHints = ({ 
  preconnect = [], 
  prefetch = [], 
  preload = [] 
}) => {
  React.useEffect(() => {
    // Preconnect to external domains
    preconnect.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'preconnect';
      link.href = url;
      document.head.appendChild(link);
    });

    // Prefetch resources
    prefetch.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = url;
      document.head.appendChild(link);
    });

    // Preload critical resources
    preload.forEach(resource => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = resource.href;
      link.as = resource.as;
      if (resource.type) link.type = resource.type;
      document.head.appendChild(link);
    });
  }, [preconnect, prefetch, preload]);

  return null;
};

/**
 * Bundle Size Analyzer (Development only)
 */
export const BundleAnalyzer = () => {
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // Analyze bundle size and log warnings
      const checkBundleSize = () => {
        const scripts = document.querySelectorAll('script[src]');
        scripts.forEach(script => {
          fetch(script.src, { method: 'HEAD' })
            .then(response => {
              const size = response.headers.get('content-length');
              if (size && parseInt(size) > 500000) { // 500KB
                console.warn(`Large bundle detected: ${script.src} (${(size / 1024).toFixed(2)}KB)`);
              }
            })
            .catch(() => {}); // Ignore CORS errors
        });
      };

      setTimeout(checkBundleSize, 1000);
    }
  }, []);

  return null;
};

export default {
  OptimizedImage,
  LazyLoad,
  withLazyLoading,
  PreloadResources,
  VirtualScrollList,
  DebouncedInput,
  MemoizedComponent,
  usePerformanceMonitor,
  ResourceHints,
  BundleAnalyzer
};
