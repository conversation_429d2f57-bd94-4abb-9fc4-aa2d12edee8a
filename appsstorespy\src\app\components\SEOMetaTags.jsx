import Head from 'next/head';
import { generateCrossLinkStructuredData } from '../utils/crossLinking';

/**
 * Comprehensive SEO Meta Tags Component
 * Includes Open Graph, Twitter Cards, and structured data
 */
const SEOMetaTags = ({
  title,
  description,
  keywords = [],
  image,
  url,
  type = 'website',
  appDetails = null,
  relatedLinks = [],
  author = 'AppsStoreSpy',
  siteName = 'AppsStoreSpy',
  locale = 'en_US',
  publishedTime = null,
  modifiedTime = null
}) => {
  // Generate structured data if app details are provided
  const structuredData = appDetails ? 
    generateCrossLinkStructuredData(appDetails, relatedLinks) : null;

  // Default values
  const defaultTitle = 'AppsStoreSpy - App Analytics & Market Intelligence';
  const defaultDescription = 'Comprehensive app analytics and market intelligence platform. Track performance, analyze competitors, and optimize your app store presence.';
  const defaultImage = '/images/appsstorespy-og.jpg';
  const defaultUrl = 'https://appsstorespy.com';

  const finalTitle = title || defaultTitle;
  const finalDescription = description || defaultDescription;
  const finalImage = image || defaultImage;
  const finalUrl = url || defaultUrl;

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{finalTitle}</title>
      <meta name="description" content={finalDescription} />
      {keywords.length > 0 && (
        <meta name="keywords" content={keywords.join(', ')} />
      )}
      <meta name="author" content={author} />
      <meta name="robots" content="index, follow" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="language" content="English" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={finalUrl} />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={finalTitle} />
      <meta property="og:description" content={finalDescription} />
      <meta property="og:image" content={finalImage} />
      <meta property="og:url" content={finalUrl} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content={locale} />
      {publishedTime && (
        <meta property="article:published_time" content={publishedTime} />
      )}
      {modifiedTime && (
        <meta property="article:modified_time" content={modifiedTime} />
      )}
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={finalTitle} />
      <meta name="twitter:description" content={finalDescription} />
      <meta name="twitter:image" content={finalImage} />
      <meta name="twitter:url" content={finalUrl} />
      <meta name="twitter:site" content="@appsstorespy" />
      <meta name="twitter:creator" content="@appsstorespy" />
      
      {/* Additional Meta Tags for Better SEO */}
      <meta name="theme-color" content="#00A3FF" />
      <meta name="msapplication-TileColor" content="#00A3FF" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content={siteName} />
      
      {/* Favicon and Icons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />
      
      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData)
          }}
        />
      )}
      
      {/* Additional Structured Data for Organization */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": siteName,
            "url": defaultUrl,
            "logo": `${defaultUrl}/logo.png`,
            "description": defaultDescription,
            "sameAs": [
              "https://twitter.com/appsstorespy",
              "https://linkedin.com/company/appsstorespy"
            ],
            "contactPoint": {
              "@type": "ContactPoint",
              "contactType": "customer service",
              "email": "<EMAIL>"
            }
          })
        }}
      />
      
      {/* Website Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebSite",
            "name": siteName,
            "url": defaultUrl,
            "description": defaultDescription,
            "potentialAction": {
              "@type": "SearchAction",
              "target": {
                "@type": "EntryPoint",
                "urlTemplate": `${defaultUrl}/search?q={search_term_string}`
              },
              "query-input": "required name=search_term_string"
            }
          })
        }}
      />
    </Head>
  );
};

/**
 * App-specific SEO Meta Tags
 */
export const AppSEOMetaTags = ({ appDetails, relatedLinks = [] }) => {
  if (!appDetails) return null;

  const title = `${appDetails.title} - App Analytics & Performance Insights | AppsStoreSpy`;
  const description = `Comprehensive analytics for ${appDetails.title}. Track downloads, ratings, reviews, and market performance. Compare with competitors and optimize your app strategy.`;
  const keywords = [
    appDetails.title,
    'app analytics',
    'mobile app performance',
    'app store optimization',
    'ASO',
    appDetails.category,
    'app market intelligence',
    'competitor analysis'
  ];
  const url = `https://appsstorespy.com/app-details/${appDetails.appId}`;
  const image = appDetails.icon || '/images/app-analytics-og.jpg';

  return (
    <SEOMetaTags
      title={title}
      description={description}
      keywords={keywords}
      image={image}
      url={url}
      type="article"
      appDetails={appDetails}
      relatedLinks={relatedLinks}
      publishedTime={appDetails.publishedDate}
      modifiedTime={appDetails.lastUpdated}
    />
  );
};

/**
 * Category-specific SEO Meta Tags
 */
export const CategorySEOMetaTags = ({ category, apps = [] }) => {
  const title = `Top ${category} Apps - Analytics & Performance Data | AppsStoreSpy`;
  const description = `Discover the best ${category} apps with comprehensive analytics. Track performance, downloads, ratings, and market trends for ${category} applications.`;
  const keywords = [
    `${category} apps`,
    'top apps',
    'app analytics',
    'mobile app performance',
    'app rankings',
    'app store data',
    category.toLowerCase()
  ];
  const url = `https://appsstorespy.com/category/${category.toLowerCase()}`;

  return (
    <SEOMetaTags
      title={title}
      description={description}
      keywords={keywords}
      url={url}
      type="website"
    />
  );
};

export default SEOMetaTags;
