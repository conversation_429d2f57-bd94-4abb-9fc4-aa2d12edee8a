"use client";
import React from 'react';

/**
 * Semantic HTML Layout Component for SmartTools
 * Provides proper semantic structure for better SEO and accessibility
 */
const SemanticLayout = ({ 
  children,
  headerContent,
  navContent,
  asideContent,
  footerContent,
  className = '',
  mainClassName = '',
  role = null
}) => {
  return (
    <div className={`semantic-layout flex flex-col min-h-screen ${className}`}>
      {/* Header Section */}
      {headerContent && (
        <header 
          role="banner"
          className="sticky top-0 z-50 bg-white border-b border-gray-200 shadow-sm"
        >
          {headerContent}
        </header>
      )}

      {/* Navigation Section */}
      {navContent && (
        <nav 
          role="navigation"
          aria-label="Main navigation"
          className="bg-gray-50 border-b border-gray-200"
        >
          {navContent}
        </nav>
      )}

      {/* Main Content Area */}
      <div className="flex flex-1 flex-col lg:flex-row">
        {/* Sidebar/Aside Content */}
        {asideContent && (
          <aside 
            role="complementary"
            aria-label="Sidebar content"
            className="w-full lg:w-80 bg-white border-r border-gray-200 p-4"
          >
            {asideContent}
          </aside>
        )}

        {/* Main Content */}
        <main 
          role={role || "main"}
          aria-label="Main content"
          className={`flex-1 p-4 lg:p-8 bg-gray-50 ${mainClassName}`}
        >
          {children}
        </main>
      </div>

      {/* Footer Section */}
      {footerContent && (
        <footer 
          role="contentinfo"
          className="mt-auto bg-white border-t border-gray-200"
        >
          {footerContent}
        </footer>
      )}
    </div>
  );
};

/**
 * Article Layout Component for SmartTools
 * Semantic structure for blog/article content
 */
export const ArticleLayout = ({ 
  title,
  subtitle,
  author,
  publishDate,
  lastModified,
  content,
  relatedContent,
  breadcrumbs,
  tags = [],
  readTime,
  className = ''
}) => {
  return (
    <article 
      className={`article-layout max-w-4xl mx-auto ${className}`}
      itemScope 
      itemType="https://schema.org/Article"
    >
      {/* Breadcrumbs */}
      {breadcrumbs && (
        <nav aria-label="Breadcrumb" className="mb-6">
          <ol 
            className="flex items-center space-x-2 text-sm text-gray-600"
            itemScope 
            itemType="https://schema.org/BreadcrumbList"
          >
            {breadcrumbs.map((crumb, index) => (
              <li 
                key={index}
                itemProp="itemListElement" 
                itemScope 
                itemType="https://schema.org/ListItem"
                className="flex items-center"
              >
                {index > 0 && <span className="mx-2 text-gray-400">→</span>}
                {crumb.href ? (
                  <a 
                    href={crumb.href}
                    itemProp="item"
                    className="hover:text-green-600 transition-colors"
                  >
                    <span itemProp="name">{crumb.name}</span>
                  </a>
                ) : (
                  <span itemProp="name" className="text-gray-900 font-medium">{crumb.name}</span>
                )}
                <meta itemProp="position" content={index + 1} />
              </li>
            ))}
          </ol>
        </nav>
      )}

      {/* Article Header */}
      <header className="mb-8 text-center">
        <h1 
          className="text-4xl font-bold text-gray-900 mb-4 leading-tight"
          itemProp="headline"
        >
          {title}
        </h1>
        
        {subtitle && (
          <h2 
            className="text-xl text-gray-600 mb-6 leading-relaxed"
            itemProp="alternativeHeadline"
          >
            {subtitle}
          </h2>
        )}

        {/* Article Meta */}
        <div className="flex flex-wrap justify-center items-center gap-6 text-sm text-gray-500 mb-6">
          {author && (
            <div 
              itemProp="author" 
              itemScope 
              itemType="https://schema.org/Person"
              className="flex items-center"
            >
              👤 By <span itemProp="name" className="font-medium ml-1">{author}</span>
            </div>
          )}
          
          {publishDate && (
            <time 
              dateTime={publishDate}
              itemProp="datePublished"
              className="flex items-center"
            >
              📅 {new Date(publishDate).toLocaleDateString()}
            </time>
          )}
          
          {readTime && (
            <span className="flex items-center">
              ⏱️ {readTime} min read
            </span>
          )}
          
          {lastModified && (
            <time 
              dateTime={lastModified}
              itemProp="dateModified"
              className="flex items-center"
            >
              🔄 Updated: {new Date(lastModified).toLocaleDateString()}
            </time>
          )}
        </div>

        {/* Tags */}
        {tags.length > 0 && (
          <div className="flex flex-wrap justify-center gap-2">
            {tags.map((tag, index) => (
              <span 
                key={index}
                className="inline-block bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium"
                itemProp="keywords"
              >
                #{tag}
              </span>
            ))}
          </div>
        )}
      </header>

      {/* Article Content */}
      <section 
        className="prose prose-lg max-w-none mb-12"
        itemProp="articleBody"
      >
        {content}
      </section>

      {/* Related Content */}
      {relatedContent && (
        <aside 
          className="mt-12 p-8 bg-gradient-to-r from-green-50 to-blue-50 rounded-xl border border-green-200"
          aria-label="Related content"
        >
          <h3 className="text-xl font-bold mb-6 text-gray-900">Related AI Tools & Resources</h3>
          {relatedContent}
        </aside>
      )}
    </article>
  );
};

/**
 * Tool Details Layout Component for AI tool pages
 * Semantic structure for tool detail pages
 */
export const ToolDetailsLayout = ({ 
  toolInfo,
  screenshots,
  description,
  features,
  pricing,
  reviews,
  relatedTools,
  trySection,
  className = ''
}) => {
  return (
    <article 
      className={`tool-details-layout max-w-6xl mx-auto ${className}`}
      itemScope 
      itemType="https://schema.org/SoftwareApplication"
    >
      {/* Tool Header */}
      <header className="mb-10 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-8">
        <div className="flex items-start gap-8">
          {toolInfo.logo && (
            <img 
              src={toolInfo.logo}
              alt={`${toolInfo.title} logo`}
              className="w-24 h-24 rounded-2xl shadow-lg"
              itemProp="image"
            />
          )}
          <div className="flex-1">
            <h1 
              className="text-4xl font-bold text-gray-900 mb-3"
              itemProp="name"
            >
              {toolInfo.title}
            </h1>
            {toolInfo.tagline && (
              <p className="text-xl text-gray-600 mb-4" itemProp="description">
                {toolInfo.tagline}
              </p>
            )}
            <div className="flex flex-wrap gap-3">
              {toolInfo.category && (
                <span 
                  className="inline-block bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-medium"
                  itemProp="applicationCategory"
                >
                  🤖 {toolInfo.category}
                </span>
              )}
              {toolInfo.pricing && (
                <span 
                  className="inline-block bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium"
                >
                  💰 {toolInfo.pricing}
                </span>
              )}
              {toolInfo.rating && (
                <span 
                  className="inline-block bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium"
                  itemProp="aggregateRating"
                  itemScope
                  itemType="https://schema.org/AggregateRating"
                >
                  ⭐ <span itemProp="ratingValue">{toolInfo.rating}</span>/5
                </span>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Try/Demo Section */}
      {trySection && (
        <section aria-label="Try tool" className="mb-10">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Try {toolInfo.title}</h2>
          <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-200">
            {trySection}
          </div>
        </section>
      )}

      {/* Screenshots/Demo Section */}
      {screenshots && (
        <section aria-label="Screenshots" className="mb-10">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Screenshots & Demo</h2>
          <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-200">
            {screenshots}
          </div>
        </section>
      )}

      {/* Description Section */}
      {description && (
        <section aria-label="Description" className="mb-10">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">About {toolInfo.title}</h2>
          <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-200">
            {description}
          </div>
        </section>
      )}

      {/* Features Section */}
      {features && (
        <section aria-label="Features" className="mb-10">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Key Features</h2>
          <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-200">
            {features}
          </div>
        </section>
      )}

      {/* Pricing Section */}
      {pricing && (
        <section aria-label="Pricing" className="mb-10">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Pricing Plans</h2>
          <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-200">
            {pricing}
          </div>
        </section>
      )}

      {/* Reviews Section */}
      {reviews && (
        <section aria-label="Reviews" className="mb-10">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">User Reviews</h2>
          <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-200">
            {reviews}
          </div>
        </section>
      )}

      {/* Related Tools */}
      {relatedTools && (
        <aside aria-label="Related tools" className="mt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Related AI Tools</h2>
          <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-200">
            {relatedTools}
          </div>
        </aside>
      )}
    </article>
  );
};

export default SemanticLayout;
