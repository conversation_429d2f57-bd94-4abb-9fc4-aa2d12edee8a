import { NextResponse } from "next/server";

export const GET = async (request,res) => {
  const url = new URL(request.url);
  const userIpAddress = url.searchParams.get("userIpAddress");
  if (!userIpAddress) {
    return NextResponse.json({ error: "userIp is required" }, { status: 400 });
  }

  try {
    let country = { countryCode: '', countryName: '' };
        const response = await fetch(`http://ip-api.com/json/${userIpAddress}`);
        const data = await response.json();
        const ipCountryCode = data.countryCode || "US";
        const ipCountry = data.country || "United States";
        country.countryCode = ipCountryCode;
        country.countryName = ipCountry;

    return NextResponse.json({ country }, { status: 200 });
  } catch (error) {
    return NextResponse.json(
      { message: "Error fetching userCountry", error },
      { status: 500 }
    );
  }
};
