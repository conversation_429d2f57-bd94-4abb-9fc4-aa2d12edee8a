import connectDB from "../../database/mongoose";
import { NextResponse } from "next/server";
import crypto from "crypto";
import User from "@/app/database/user";
import { transporter } from "@/app/configer/nodemailer";


export async function POST(req) {
  const { email } = await req.json();
  if (!email) {
    return NextResponse.json({ error: "email" }, { status: 400 });
  }
  await connectDB();
   let userExists;

  try {
    userExists = await User.findOne({ email });

    if (!userExists) {
      return NextResponse.json(
        { message: "Email doesn't exists..!" },
        { status: 201 }
      );
    }
    const resetToken = crypto.randomBytes(20).toString("hex");
    const passwordResetToken = crypto
      .createHash("sha256")
      .update(resetToken)
      .digest("hex");

    const passwordResetExpires = Date.now() + 3600000;

    userExists.resetToken = passwordResetToken;
    userExists.resetTokenExpiry = passwordResetExpires;
    // const resetUrl = `http://localhost:3000/reset-password/${resetToken}`;
    const resetUrl = `https://appsstorespy.io/reset-password/${resetToken}`

    const msg = {
      to: email,
      from: process.env.EMAIL,
      subject: "Reset Password",
      text: `Reset Password by clicking on the following URL: ${resetUrl}`,
    };

    await transporter.verify();
    await transporter.sendMail(msg);
    await userExists.save();
    return NextResponse.json(
      { message: "reset link sent in email!!" },
      { status: 200 }
    );
  } catch (e) {
    console.log(e, "not valid");
    userExists.resetToken = undefined;
    userExists.resetTokenExpiry = undefined;
    await userExists.save();
    return NextResponse.json(
      { error: "Failed to send password reset email" },
      { status: 500 }
    );
  }

}
