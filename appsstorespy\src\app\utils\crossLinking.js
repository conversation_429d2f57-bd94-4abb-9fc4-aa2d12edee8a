/**
 * Cross-linking utilities for SEO-friendly navigation between APK, AI Tools, and AppStoreSpy sites
 */

// Three-domain configuration
export const DOMAINS = {
  APK_ALL_VERSIONS: process.env.NEXT_PUBLIC_APK_ALL_VERSIONS_DOMAIN || 'https://apkdemo1.com',
  APK_LATEST: process.env.NEXT_PUBLIC_APK_LATEST_DOMAIN || 'https://apk2demo1.com',
  AI_TOOLS: process.env.NEXT_PUBLIC_AI_TOOLS_DOMAIN || 'https://apk3.demo1.com',
  APP_STORE_SPY: process.env.NEXT_PUBLIC_APP_STORE_SPY_DOMAIN || 'https://appsstorespy.com'
};

// Site information for navigation
export const SITE_INFO = {
  APK_ALL_VERSIONS: {
    name: 'APKExplorer',
    description: 'Download Android APKs - All Versions',
    domain: DOMAINS.APK_ALL_VERSIONS
  },
  APK_LATEST: {
    name: '<PERSON>KExplore<PERSON> Latest',
    description: 'Download Latest Android APKs',
    domain: DOMAINS.APK_LATEST
  },
  AI_TOOLS: {
    name: 'SmartTools',
    description: 'Discover Top AI Tools & Reviews',
    domain: DOMAINS.AI_TOOLS
  },
  APP_STORE_SPY: {
    name: 'AppsStoreSpy',
    description: 'App Analytics & Market Intelligence',
    domain: DOMAINS.APP_STORE_SPY
  }
};

// Get current site type
export const getCurrentSiteType = () => {
  return process.env.NEXT_PUBLIC_SITE_TYPE || 'app-store-spy';
};

// Check if cross-linking is enabled
export const isCrossLinkingEnabled = () => {
  return process.env.NEXT_PUBLIC_ENABLE_CROSS_LINKING === 'true';
};

/**
 * Generate APK download URL for an app from AppStoreSpy
 */
export const generateAPKDownloadURL = (appDetails) => {
  if (!isCrossLinkingEnabled()) return null;
  
  const baseURL = DOMAINS.APK_ALL_VERSIONS;
  
  if (appDetails?.appId) {
    return `${baseURL}/apps/appdetails/${appDetails.appId}?ref=appstorespy`;
  }
  
  // Fallback to search if no specific app ID
  if (appDetails?.title) {
    return `${baseURL}/app-search?q=${encodeURIComponent(appDetails.title)}&ref=appstorespy`;
  }
  
  return `${baseURL}/apps?ref=appstorespy`;
};

/**
 * Generate AI tools discovery URL for an app category
 */
export const generateAIToolsURL = (appDetails) => {
  if (!isCrossLinkingEnabled()) return null;
  
  const baseURL = DOMAINS.AI_TOOLS;
  
  // Map app categories to AI tool categories
  const categoryMapping = {
    'productivity': 'productivity',
    'business': 'business',
    'education': 'education',
    'entertainment': 'content-creation',
    'photography': 'image-generation',
    'communication': 'communication',
    'social': 'social-media',
    'finance': 'finance',
    'health': 'health',
    'lifestyle': 'lifestyle',
    'shopping': 'e-commerce',
    'travel': 'travel',
    'news': 'content-creation',
    'sports': 'analytics',
    'weather': 'analytics'
  };
  
  if (appDetails?.category) {
    const aiCategory = categoryMapping[appDetails.category.toLowerCase()] || 'productivity';
    return `${baseURL}/tools/${aiCategory}?ref=appstorespy&app=${encodeURIComponent(appDetails.appId || '')}`;
  }
  
  return `${baseURL}/tool?ref=appstorespy&app=${encodeURIComponent(appDetails?.appId || '')}`;
};

/**
 * Track cross-link clicks for analytics
 */
export const trackCrossLinkClick = (sourceType, targetType, details = {}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'cross_link_click', {
      source_site: sourceType,
      target_site: targetType,
      app_id: details.appId || '',
      category: details.category || '',
      custom_parameter_1: details.title || ''
    });
  }
};

/**
 * Generate SEO-friendly anchor text for cross-links
 */
export const generateAnchorText = (type, details) => {
  if (type === 'apk-download') {
    const appName = details?.title || 'this app';
    return `Download ${appName} APK`;
  } else if (type === 'ai-tools') {
    const category = details?.category || 'productivity';
    return `Discover AI ${category.charAt(0).toUpperCase() + category.slice(1)} Tools`;
  }
  return 'Explore More';
};

/**
 * Generate related links for app details pages
 */
export const generateRelatedLinks = (appDetails) => {
  if (!isCrossLinkingEnabled()) return [];
  
  const links = [];
  
  // APK download link
  const apkURL = generateAPKDownloadURL(appDetails);
  if (apkURL) {
    links.push({
      type: 'apk-download',
      url: apkURL,
      text: generateAnchorText('apk-download', appDetails),
      description: `Download ${appDetails?.title || 'this app'} APK for Android`,
      icon: '📱'
    });
  }
  
  // AI tools link
  const aiToolsURL = generateAIToolsURL(appDetails);
  if (aiToolsURL) {
    links.push({
      type: 'ai-tools',
      url: aiToolsURL,
      text: generateAnchorText('ai-tools', appDetails),
      description: `Discover AI tools for ${appDetails?.category || 'productivity'}`,
      icon: '🤖'
    });
  }
  
  return links;
};

/**
 * Generate structured data for cross-linking
 */
export const generateCrossLinkStructuredData = (appDetails, relatedLinks = []) => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": `${appDetails?.title || 'App'} Analytics - AppsStoreSpy`,
    "description": `Comprehensive analytics and insights for ${appDetails?.title || 'this app'} on Google Play Store`,
    "url": `${DOMAINS.APP_STORE_SPY}/app-details/${appDetails?.appId || ''}`,
    "mainEntity": {
      "@type": "SoftwareApplication",
      "name": appDetails?.title || '',
      "applicationCategory": "MobileApplication",
      "operatingSystem": "Android"
    }
  };
  
  // Add related links as mentions
  if (relatedLinks.length > 0) {
    structuredData.mentions = relatedLinks.map(link => ({
      "@type": "WebPage",
      "name": link.text,
      "url": link.url,
      "description": link.description
    }));
  }
  
  return structuredData;
};

export default {
  DOMAINS,
  SITE_INFO,
  getCurrentSiteType,
  isCrossLinkingEnabled,
  generateAPKDownloadURL,
  generateAIToolsURL,
  trackCrossLinkClick,
  generateAnchorText,
  generateRelatedLinks,
  generateCrossLinkStructuredData
};
