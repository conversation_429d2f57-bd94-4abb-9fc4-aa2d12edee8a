// middleware.ts
import { withAuth } from "next-auth/middleware";
import { NextResponse } from "next/server";

export default withAuth(
  function middleware(req) {
    const { pathname } = req.nextUrl;
    const token = req.nextauth.token;

    if (token && (pathname.startsWith("/login") || pathname.startsWith("/register"))) {
      return NextResponse.redirect(new URL("/", req.url));
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token }) => {
        return true; 
      },
    },
  }
);

export const config = {
  matcher: [
    "/app-details/:path*",
    "/login",
    "/register",
    "/top-apps",
    "/top-games",
    "/developer-apps/:path*"
  ]
};