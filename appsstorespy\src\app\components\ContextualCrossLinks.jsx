"use client";
import React from 'react';
import { <PERSON>, Card, CardContent, Typography, Button, Grid, Chip } from '@mui/material';
import { Download, SmartToy, Apps, Analytics, Launch } from '@mui/icons-material';
import { generateRelatedLinks, trackCrossLinkClick } from '../utils/crossLinking';

/**
 * Contextual Cross-Links Component for App Details Pages
 * Shows relevant links to other sites based on app information
 */
const ContextualCrossLinks = ({ 
  appDetails, 
  variant = 'card', // 'card', 'banner', 'inline'
  className = '',
  maxLinks = 3 
}) => {
  const relatedLinks = generateRelatedLinks(appDetails);
  
  if (!relatedLinks || relatedLinks.length === 0) {
    return null;
  }

  const handleLinkClick = (link) => {
    trackCrossLinkClick('app-store-spy', link.type, {
      appId: appDetails?.appId,
      title: appDetails?.title,
      category: appDetails?.category
    });
  };

  const getIcon = (type) => {
    switch (type) {
      case 'apk-download':
        return <Download />;
      case 'ai-tools':
        return <SmartToy />;
      default:
        return <Launch />;
    }
  };

  const getColor = (type) => {
    switch (type) {
      case 'apk-download':
        return 'success';
      case 'ai-tools':
        return 'secondary';
      default:
        return 'primary';
    }
  };

  // Card variant - prominent display
  if (variant === 'card') {
    return (
      <Card className={className} sx={{ mt: 3, background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)' }}>
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Apps sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              Explore Related Resources
            </Typography>
          </Box>
          
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Discover more tools and downloads related to {appDetails?.title || 'this app'}
          </Typography>

          <Grid container spacing={2}>
            {relatedLinks.slice(0, maxLinks).map((link, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Button
                  href={link.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  variant="contained"
                  color={getColor(link.type)}
                  startIcon={getIcon(link.type)}
                  fullWidth
                  sx={{
                    py: 1.5,
                    textTransform: 'none',
                    fontWeight: 500,
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: 3
                    },
                    transition: 'all 0.2s ease-in-out'
                  }}
                  onClick={() => handleLinkClick(link)}
                >
                  <Box sx={{ textAlign: 'left', width: '100%' }}>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      {link.text}
                    </Typography>
                    <Typography variant="caption" sx={{ opacity: 0.8, display: 'block' }}>
                      {link.description}
                    </Typography>
                  </Box>
                </Button>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>
    );
  }

  // Banner variant - horizontal layout
  if (variant === 'banner') {
    return (
      <Box
        className={className}
        sx={{
          background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)',
          borderRadius: 2,
          p: 3,
          mt: 3,
          color: 'white'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
              🚀 Expand Your Toolkit
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>
              Get the APK or discover AI tools for {appDetails?.category || 'productivity'}
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            {relatedLinks.slice(0, 2).map((link, index) => (
              <Button
                key={index}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                variant="contained"
                startIcon={getIcon(link.type)}
                sx={{
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.3)'
                  },
                  textTransform: 'none',
                  fontWeight: 500
                }}
                onClick={() => handleLinkClick(link)}
              >
                {link.text}
              </Button>
            ))}
          </Box>
        </Box>
      </Box>
    );
  }

  // Inline variant - compact chips
  if (variant === 'inline') {
    return (
      <Box className={className} sx={{ mt: 2 }}>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
          Related resources:
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {relatedLinks.slice(0, maxLinks).map((link, index) => (
            <Chip
              key={index}
              label={link.text}
              icon={getIcon(link.type)}
              component="a"
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              clickable
              color={getColor(link.type)}
              variant="outlined"
              sx={{
                '&:hover': {
                  backgroundColor: `${getColor(link.type)}.light`,
                  color: 'white'
                }
              }}
              onClick={() => handleLinkClick(link)}
            />
          ))}
        </Box>
      </Box>
    );
  }

  return null;
};

/**
 * App Overview Cross-Links Component
 * Specifically designed for the overview section of app details
 */
export const AppOverviewCrossLinks = ({ appDetails, className = '' }) => {
  const relatedLinks = generateRelatedLinks(appDetails);
  
  if (!relatedLinks || relatedLinks.length === 0) {
    return null;
  }

  return (
    <Box className={className} sx={{ mt: 3 }}>
      <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
        Related Resources
      </Typography>
      
      <Grid container spacing={2}>
        {relatedLinks.map((link, index) => (
          <Grid item xs={12} sm={6} key={index}>
            <Card 
              sx={{ 
                cursor: 'pointer',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 4
                }
              }}
              component="a"
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              onClick={() => trackCrossLinkClick('app-store-spy', link.type, {
                appId: appDetails?.appId,
                title: appDetails?.title,
                category: appDetails?.category
              })}
            >
              <CardContent sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Box sx={{ 
                    backgroundColor: `${link.type === 'apk-download' ? 'success' : 'secondary'}.light`,
                    borderRadius: '50%',
                    p: 1,
                    mr: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    {link.icon}
                  </Box>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>
                    {link.text}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {link.description}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default ContextualCrossLinks;
