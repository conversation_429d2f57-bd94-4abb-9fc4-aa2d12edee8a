"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@fortawesome";
exports.ids = ["vendor-chunks/@fortawesome"];
exports.modules = {

/***/ "(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/styles.css":
/*!*******************************************************************!*\
  !*** ./node_modules/@fortawesome/fontawesome-svg-core/styles.css ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f372c3215964\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGZvcnRhd2Vzb21lL2ZvbnRhd2Vzb21lLXN2Zy1jb3JlL3N0eWxlcy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90b29scGxhdGUuYWkvLi9ub2RlX21vZHVsZXMvQGZvcnRhd2Vzb21lL2ZvbnRhd2Vzb21lLXN2Zy1jb3JlL3N0eWxlcy5jc3M/NmYxZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImYzNzJjMzIxNTk2NFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/styles.css\n");

/***/ }),

/***/ "(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@fortawesome/fontawesome-svg-core/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   config: () => (/* binding */ config$1),\n/* harmony export */   counter: () => (/* binding */ counter),\n/* harmony export */   dom: () => (/* binding */ dom$1),\n/* harmony export */   findIconDefinition: () => (/* binding */ findIconDefinition$1),\n/* harmony export */   icon: () => (/* binding */ icon),\n/* harmony export */   layer: () => (/* binding */ layer),\n/* harmony export */   library: () => (/* binding */ library$1),\n/* harmony export */   noAuto: () => (/* binding */ noAuto$1),\n/* harmony export */   parse: () => (/* binding */ parse$1),\n/* harmony export */   text: () => (/* binding */ text),\n/* harmony export */   toHtml: () => (/* binding */ toHtml$1)\n/* harmony export */ });\n/*!\n * Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)\n * Copyright 2024 Fonticons, Inc.\n */\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _inherits(t, e) {\n  if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\n  t.prototype = Object.create(e && e.prototype, {\n    constructor: {\n      value: t,\n      writable: !0,\n      configurable: !0\n    }\n  }), Object.defineProperty(t, \"prototype\", {\n    writable: !1\n  }), e && _setPrototypeOf(t, e);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _wrapRegExp() {\n  _wrapRegExp = function (e, r) {\n    return new BabelRegExp(e, void 0, r);\n  };\n  var e = RegExp.prototype,\n    r = new WeakMap();\n  function BabelRegExp(e, t, p) {\n    var o = RegExp(e, t);\n    return r.set(o, p || r.get(e)), _setPrototypeOf(o, BabelRegExp.prototype);\n  }\n  function buildGroups(e, t) {\n    var p = r.get(t);\n    return Object.keys(p).reduce(function (r, t) {\n      var o = p[t];\n      if (\"number\" == typeof o) r[t] = e[o];else {\n        for (var i = 0; void 0 === e[o[i]] && i + 1 < o.length;) i++;\n        r[t] = e[o[i]];\n      }\n      return r;\n    }, Object.create(null));\n  }\n  return _inherits(BabelRegExp, RegExp), BabelRegExp.prototype.exec = function (r) {\n    var t = e.exec.call(this, r);\n    if (t) {\n      t.groups = buildGroups(t, this);\n      var p = t.indices;\n      p && (p.groups = buildGroups(p, this));\n    }\n    return t;\n  }, BabelRegExp.prototype[Symbol.replace] = function (t, p) {\n    if (\"string\" == typeof p) {\n      var o = r.get(this);\n      return e[Symbol.replace].call(this, t, p.replace(/\\$<([^>]+)>/g, function (e, r) {\n        var t = o[r];\n        return \"$\" + (Array.isArray(t) ? t.join(\"$\") : t);\n      }));\n    }\n    if (\"function\" == typeof p) {\n      var i = this;\n      return e[Symbol.replace].call(this, t, function () {\n        var e = arguments;\n        return \"object\" != typeof e[e.length - 1] && (e = [].slice.call(e)).push(buildGroups(e, i)), p.apply(this, e);\n      });\n    }\n    return e[Symbol.replace].call(this, t, p);\n  }, _wrapRegExp.apply(this, arguments);\n}\n\nconst noop = () => {};\nlet _WINDOW = {};\nlet _DOCUMENT = {};\nlet _MUTATION_OBSERVER = null;\nlet _PERFORMANCE = {\n  mark: noop,\n  measure: noop\n};\ntry {\n  if (typeof window !== 'undefined') _WINDOW = window;\n  if (typeof document !== 'undefined') _DOCUMENT = document;\n  if (typeof MutationObserver !== 'undefined') _MUTATION_OBSERVER = MutationObserver;\n  if (typeof performance !== 'undefined') _PERFORMANCE = performance;\n} catch (e) {}\nconst {\n  userAgent = ''\n} = _WINDOW.navigator || {};\nconst WINDOW = _WINDOW;\nconst DOCUMENT = _DOCUMENT;\nconst MUTATION_OBSERVER = _MUTATION_OBSERVER;\nconst PERFORMANCE = _PERFORMANCE;\nconst IS_BROWSER = !!WINDOW.document;\nconst IS_DOM = !!DOCUMENT.documentElement && !!DOCUMENT.head && typeof DOCUMENT.addEventListener === 'function' && typeof DOCUMENT.createElement === 'function';\nconst IS_IE = ~userAgent.indexOf('MSIE') || ~userAgent.indexOf('Trident/');\n\nvar p = /fa(s|r|l|t|d|dr|dl|dt|b|k|kd|ss|sr|sl|st|sds|sdr|sdl|sdt)?[\\-\\ ]/,\n  g = /Font ?Awesome ?([56 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp Duotone|Sharp|Kit)?.*/i;\nvar S = {\n    classic: {\n      fa: \"solid\",\n      fas: \"solid\",\n      \"fa-solid\": \"solid\",\n      far: \"regular\",\n      \"fa-regular\": \"regular\",\n      fal: \"light\",\n      \"fa-light\": \"light\",\n      fat: \"thin\",\n      \"fa-thin\": \"thin\",\n      fab: \"brands\",\n      \"fa-brands\": \"brands\"\n    },\n    duotone: {\n      fa: \"solid\",\n      fad: \"solid\",\n      \"fa-solid\": \"solid\",\n      \"fa-duotone\": \"solid\",\n      fadr: \"regular\",\n      \"fa-regular\": \"regular\",\n      fadl: \"light\",\n      \"fa-light\": \"light\",\n      fadt: \"thin\",\n      \"fa-thin\": \"thin\"\n    },\n    sharp: {\n      fa: \"solid\",\n      fass: \"solid\",\n      \"fa-solid\": \"solid\",\n      fasr: \"regular\",\n      \"fa-regular\": \"regular\",\n      fasl: \"light\",\n      \"fa-light\": \"light\",\n      fast: \"thin\",\n      \"fa-thin\": \"thin\"\n    },\n    \"sharp-duotone\": {\n      fa: \"solid\",\n      fasds: \"solid\",\n      \"fa-solid\": \"solid\",\n      fasdr: \"regular\",\n      \"fa-regular\": \"regular\",\n      fasdl: \"light\",\n      \"fa-light\": \"light\",\n      fasdt: \"thin\",\n      \"fa-thin\": \"thin\"\n    }\n  },\n  A = {\n    GROUP: \"duotone-group\",\n    SWAP_OPACITY: \"swap-opacity\",\n    PRIMARY: \"primary\",\n    SECONDARY: \"secondary\"\n  },\n  P = [\"fa-classic\", \"fa-duotone\", \"fa-sharp\", \"fa-sharp-duotone\"];\nvar s = \"classic\",\n  t = \"duotone\",\n  r = \"sharp\",\n  o = \"sharp-duotone\",\n  L = [s, t, r, o];\nvar G = {\n    classic: {\n      900: \"fas\",\n      400: \"far\",\n      normal: \"far\",\n      300: \"fal\",\n      100: \"fat\"\n    },\n    duotone: {\n      900: \"fad\",\n      400: \"fadr\",\n      300: \"fadl\",\n      100: \"fadt\"\n    },\n    sharp: {\n      900: \"fass\",\n      400: \"fasr\",\n      300: \"fasl\",\n      100: \"fast\"\n    },\n    \"sharp-duotone\": {\n      900: \"fasds\",\n      400: \"fasdr\",\n      300: \"fasdl\",\n      100: \"fasdt\"\n    }\n  };\nvar lt = {\n    \"Font Awesome 6 Free\": {\n      900: \"fas\",\n      400: \"far\"\n    },\n    \"Font Awesome 6 Pro\": {\n      900: \"fas\",\n      400: \"far\",\n      normal: \"far\",\n      300: \"fal\",\n      100: \"fat\"\n    },\n    \"Font Awesome 6 Brands\": {\n      400: \"fab\",\n      normal: \"fab\"\n    },\n    \"Font Awesome 6 Duotone\": {\n      900: \"fad\",\n      400: \"fadr\",\n      normal: \"fadr\",\n      300: \"fadl\",\n      100: \"fadt\"\n    },\n    \"Font Awesome 6 Sharp\": {\n      900: \"fass\",\n      400: \"fasr\",\n      normal: \"fasr\",\n      300: \"fasl\",\n      100: \"fast\"\n    },\n    \"Font Awesome 6 Sharp Duotone\": {\n      900: \"fasds\",\n      400: \"fasdr\",\n      normal: \"fasdr\",\n      300: \"fasdl\",\n      100: \"fasdt\"\n    }\n  };\nvar pt = new Map([[\"classic\", {\n    defaultShortPrefixId: \"fas\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\", \"brands\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }], [\"sharp\", {\n    defaultShortPrefixId: \"fass\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }], [\"duotone\", {\n    defaultShortPrefixId: \"fad\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }], [\"sharp-duotone\", {\n    defaultShortPrefixId: \"fasds\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }]]),\n  xt = {\n    classic: {\n      solid: \"fas\",\n      regular: \"far\",\n      light: \"fal\",\n      thin: \"fat\",\n      brands: \"fab\"\n    },\n    duotone: {\n      solid: \"fad\",\n      regular: \"fadr\",\n      light: \"fadl\",\n      thin: \"fadt\"\n    },\n    sharp: {\n      solid: \"fass\",\n      regular: \"fasr\",\n      light: \"fasl\",\n      thin: \"fast\"\n    },\n    \"sharp-duotone\": {\n      solid: \"fasds\",\n      regular: \"fasdr\",\n      light: \"fasdl\",\n      thin: \"fasdt\"\n    }\n  };\nvar Ft = [\"fak\", \"fa-kit\", \"fakd\", \"fa-kit-duotone\"],\n  St = {\n    kit: {\n      fak: \"kit\",\n      \"fa-kit\": \"kit\"\n    },\n    \"kit-duotone\": {\n      fakd: \"kit-duotone\",\n      \"fa-kit-duotone\": \"kit-duotone\"\n    }\n  },\n  At = [\"kit\"];\nvar Ct = {\n  kit: {\n    \"fa-kit\": \"fak\"\n  },\n  \"kit-duotone\": {\n    \"fa-kit-duotone\": \"fakd\"\n  }\n};\nvar Lt = [\"fak\", \"fakd\"],\n  Wt = {\n    kit: {\n      fak: \"fa-kit\"\n    },\n    \"kit-duotone\": {\n      fakd: \"fa-kit-duotone\"\n    }\n  };\nvar Et = {\n    kit: {\n      kit: \"fak\"\n    },\n    \"kit-duotone\": {\n      \"kit-duotone\": \"fakd\"\n    }\n  };\n\nvar t$1 = {\n    GROUP: \"duotone-group\",\n    SWAP_OPACITY: \"swap-opacity\",\n    PRIMARY: \"primary\",\n    SECONDARY: \"secondary\"\n  },\n  r$1 = [\"fa-classic\", \"fa-duotone\", \"fa-sharp\", \"fa-sharp-duotone\"];\nvar bt$1 = [\"fak\", \"fa-kit\", \"fakd\", \"fa-kit-duotone\"];\nvar Yt = {\n    \"Font Awesome Kit\": {\n      400: \"fak\",\n      normal: \"fak\"\n    },\n    \"Font Awesome Kit Duotone\": {\n      400: \"fakd\",\n      normal: \"fakd\"\n    }\n  };\nvar ua = {\n    classic: {\n      \"fa-brands\": \"fab\",\n      \"fa-duotone\": \"fad\",\n      \"fa-light\": \"fal\",\n      \"fa-regular\": \"far\",\n      \"fa-solid\": \"fas\",\n      \"fa-thin\": \"fat\"\n    },\n    duotone: {\n      \"fa-regular\": \"fadr\",\n      \"fa-light\": \"fadl\",\n      \"fa-thin\": \"fadt\"\n    },\n    sharp: {\n      \"fa-solid\": \"fass\",\n      \"fa-regular\": \"fasr\",\n      \"fa-light\": \"fasl\",\n      \"fa-thin\": \"fast\"\n    },\n    \"sharp-duotone\": {\n      \"fa-solid\": \"fasds\",\n      \"fa-regular\": \"fasdr\",\n      \"fa-light\": \"fasdl\",\n      \"fa-thin\": \"fasdt\"\n    }\n  },\n  I$1 = {\n    classic: [\"fas\", \"far\", \"fal\", \"fat\", \"fad\"],\n    duotone: [\"fadr\", \"fadl\", \"fadt\"],\n    sharp: [\"fass\", \"fasr\", \"fasl\", \"fast\"],\n    \"sharp-duotone\": [\"fasds\", \"fasdr\", \"fasdl\", \"fasdt\"]\n  },\n  ga = {\n    classic: {\n      fab: \"fa-brands\",\n      fad: \"fa-duotone\",\n      fal: \"fa-light\",\n      far: \"fa-regular\",\n      fas: \"fa-solid\",\n      fat: \"fa-thin\"\n    },\n    duotone: {\n      fadr: \"fa-regular\",\n      fadl: \"fa-light\",\n      fadt: \"fa-thin\"\n    },\n    sharp: {\n      fass: \"fa-solid\",\n      fasr: \"fa-regular\",\n      fasl: \"fa-light\",\n      fast: \"fa-thin\"\n    },\n    \"sharp-duotone\": {\n      fasds: \"fa-solid\",\n      fasdr: \"fa-regular\",\n      fasdl: \"fa-light\",\n      fasdt: \"fa-thin\"\n    }\n  },\n  x = [\"fa-solid\", \"fa-regular\", \"fa-light\", \"fa-thin\", \"fa-duotone\", \"fa-brands\"],\n  Ia = [\"fa\", \"fas\", \"far\", \"fal\", \"fat\", \"fad\", \"fadr\", \"fadl\", \"fadt\", \"fab\", \"fass\", \"fasr\", \"fasl\", \"fast\", \"fasds\", \"fasdr\", \"fasdl\", \"fasdt\", ...r$1, ...x],\n  m$1 = [\"solid\", \"regular\", \"light\", \"thin\", \"duotone\", \"brands\"],\n  c$1 = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],\n  F$1 = c$1.concat([11, 12, 13, 14, 15, 16, 17, 18, 19, 20]),\n  ma = [...Object.keys(I$1), ...m$1, \"2xs\", \"xs\", \"sm\", \"lg\", \"xl\", \"2xl\", \"beat\", \"border\", \"fade\", \"beat-fade\", \"bounce\", \"flip-both\", \"flip-horizontal\", \"flip-vertical\", \"flip\", \"fw\", \"inverse\", \"layers-counter\", \"layers-text\", \"layers\", \"li\", \"pull-left\", \"pull-right\", \"pulse\", \"rotate-180\", \"rotate-270\", \"rotate-90\", \"rotate-by\", \"shake\", \"spin-pulse\", \"spin-reverse\", \"spin\", \"stack-1x\", \"stack-2x\", \"stack\", \"ul\", t$1.GROUP, t$1.SWAP_OPACITY, t$1.PRIMARY, t$1.SECONDARY].concat(c$1.map(a => \"\".concat(a, \"x\"))).concat(F$1.map(a => \"w-\".concat(a)));\nvar wa = {\n    \"Font Awesome 5 Free\": {\n      900: \"fas\",\n      400: \"far\"\n    },\n    \"Font Awesome 5 Pro\": {\n      900: \"fas\",\n      400: \"far\",\n      normal: \"far\",\n      300: \"fal\"\n    },\n    \"Font Awesome 5 Brands\": {\n      400: \"fab\",\n      normal: \"fab\"\n    },\n    \"Font Awesome 5 Duotone\": {\n      900: \"fad\"\n    }\n  };\n\nconst NAMESPACE_IDENTIFIER = '___FONT_AWESOME___';\nconst UNITS_IN_GRID = 16;\nconst DEFAULT_CSS_PREFIX = 'fa';\nconst DEFAULT_REPLACEMENT_CLASS = 'svg-inline--fa';\nconst DATA_FA_I2SVG = 'data-fa-i2svg';\nconst DATA_FA_PSEUDO_ELEMENT = 'data-fa-pseudo-element';\nconst DATA_FA_PSEUDO_ELEMENT_PENDING = 'data-fa-pseudo-element-pending';\nconst DATA_PREFIX = 'data-prefix';\nconst DATA_ICON = 'data-icon';\nconst HTML_CLASS_I2SVG_BASE_CLASS = 'fontawesome-i2svg';\nconst MUTATION_APPROACH_ASYNC = 'async';\nconst TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS = ['HTML', 'HEAD', 'STYLE', 'SCRIPT'];\nconst PRODUCTION = (() => {\n  try {\n    return \"development\" === 'production';\n  } catch (e$$1) {\n    return false;\n  }\n})();\nfunction familyProxy(obj) {\n  // Defaults to the classic family if family is not available\n  return new Proxy(obj, {\n    get(target, prop) {\n      return prop in target ? target[prop] : target[s];\n    }\n  });\n}\nconst _PREFIX_TO_STYLE = _objectSpread2({}, S);\n\n// We changed FACSSClassesToStyleId in the icons repo to be canonical and as such, \"classic\" family does not have any\n// duotone styles.  But we do still need duotone in _PREFIX_TO_STYLE below, so we are manually adding\n// {'fa-duotone': 'duotone'}\n_PREFIX_TO_STYLE[s] = _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, {\n  'fa-duotone': 'duotone'\n}), S[s]), St['kit']), St['kit-duotone']);\nconst PREFIX_TO_STYLE = familyProxy(_PREFIX_TO_STYLE);\nconst _STYLE_TO_PREFIX = _objectSpread2({}, xt);\n\n// We changed FAStyleIdToShortPrefixId in the icons repo to be canonical and as such, \"classic\" family does not have any\n// duotone styles.  But we do still need duotone in _STYLE_TO_PREFIX below, so we are manually adding {duotone: 'fad'}\n_STYLE_TO_PREFIX[s] = _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, {\n  duotone: 'fad'\n}), _STYLE_TO_PREFIX[s]), Et['kit']), Et['kit-duotone']);\nconst STYLE_TO_PREFIX = familyProxy(_STYLE_TO_PREFIX);\nconst _PREFIX_TO_LONG_STYLE = _objectSpread2({}, ga);\n_PREFIX_TO_LONG_STYLE[s] = _objectSpread2(_objectSpread2({}, _PREFIX_TO_LONG_STYLE[s]), Wt['kit']);\nconst PREFIX_TO_LONG_STYLE = familyProxy(_PREFIX_TO_LONG_STYLE);\nconst _LONG_STYLE_TO_PREFIX = _objectSpread2({}, ua);\n_LONG_STYLE_TO_PREFIX[s] = _objectSpread2(_objectSpread2({}, _LONG_STYLE_TO_PREFIX[s]), Ct['kit']);\nconst LONG_STYLE_TO_PREFIX = familyProxy(_LONG_STYLE_TO_PREFIX);\nconst ICON_SELECTION_SYNTAX_PATTERN = p; // eslint-disable-line no-useless-escape\n\nconst LAYERS_TEXT_CLASSNAME = 'fa-layers-text';\nconst FONT_FAMILY_PATTERN = g;\nconst _FONT_WEIGHT_TO_PREFIX = _objectSpread2({}, G);\nconst FONT_WEIGHT_TO_PREFIX = familyProxy(_FONT_WEIGHT_TO_PREFIX);\nconst ATTRIBUTES_WATCHED_FOR_MUTATION = ['class', 'data-prefix', 'data-icon', 'data-fa-transform', 'data-fa-mask'];\nconst DUOTONE_CLASSES = A;\nconst RESERVED_CLASSES = [...At, ...ma];\n\nconst initial = WINDOW.FontAwesomeConfig || {};\nfunction getAttrConfig(attr) {\n  var element = DOCUMENT.querySelector('script[' + attr + ']');\n  if (element) {\n    return element.getAttribute(attr);\n  }\n}\nfunction coerce(val) {\n  // Getting an empty string will occur if the attribute is set on the HTML tag but without a value\n  // We'll assume that this is an indication that it should be toggled to true\n  if (val === '') return true;\n  if (val === 'false') return false;\n  if (val === 'true') return true;\n  return val;\n}\nif (DOCUMENT && typeof DOCUMENT.querySelector === 'function') {\n  const attrs = [['data-family-prefix', 'familyPrefix'], ['data-css-prefix', 'cssPrefix'], ['data-family-default', 'familyDefault'], ['data-style-default', 'styleDefault'], ['data-replacement-class', 'replacementClass'], ['data-auto-replace-svg', 'autoReplaceSvg'], ['data-auto-add-css', 'autoAddCss'], ['data-auto-a11y', 'autoA11y'], ['data-search-pseudo-elements', 'searchPseudoElements'], ['data-observe-mutations', 'observeMutations'], ['data-mutate-approach', 'mutateApproach'], ['data-keep-original-source', 'keepOriginalSource'], ['data-measure-performance', 'measurePerformance'], ['data-show-missing-icons', 'showMissingIcons']];\n  attrs.forEach(_ref => {\n    let [attr, key] = _ref;\n    const val = coerce(getAttrConfig(attr));\n    if (val !== undefined && val !== null) {\n      initial[key] = val;\n    }\n  });\n}\nconst _default = {\n  styleDefault: 'solid',\n  familyDefault: s,\n  cssPrefix: DEFAULT_CSS_PREFIX,\n  replacementClass: DEFAULT_REPLACEMENT_CLASS,\n  autoReplaceSvg: true,\n  autoAddCss: true,\n  autoA11y: true,\n  searchPseudoElements: false,\n  observeMutations: true,\n  mutateApproach: 'async',\n  keepOriginalSource: true,\n  measurePerformance: false,\n  showMissingIcons: true\n};\n\n// familyPrefix is deprecated but we must still support it if present\nif (initial.familyPrefix) {\n  initial.cssPrefix = initial.familyPrefix;\n}\nconst _config = _objectSpread2(_objectSpread2({}, _default), initial);\nif (!_config.autoReplaceSvg) _config.observeMutations = false;\nconst config = {};\nObject.keys(_default).forEach(key => {\n  Object.defineProperty(config, key, {\n    enumerable: true,\n    set: function (val) {\n      _config[key] = val;\n      _onChangeCb.forEach(cb => cb(config));\n    },\n    get: function () {\n      return _config[key];\n    }\n  });\n});\n\n// familyPrefix is deprecated as of 6.2.0 and should be removed in 7.0.0\nObject.defineProperty(config, 'familyPrefix', {\n  enumerable: true,\n  set: function (val) {\n    _config.cssPrefix = val;\n    _onChangeCb.forEach(cb => cb(config));\n  },\n  get: function () {\n    return _config.cssPrefix;\n  }\n});\nWINDOW.FontAwesomeConfig = config;\nconst _onChangeCb = [];\nfunction onChange(cb) {\n  _onChangeCb.push(cb);\n  return () => {\n    _onChangeCb.splice(_onChangeCb.indexOf(cb), 1);\n  };\n}\n\nconst d$2 = UNITS_IN_GRID;\nconst meaninglessTransform = {\n  size: 16,\n  x: 0,\n  y: 0,\n  rotate: 0,\n  flipX: false,\n  flipY: false\n};\nfunction insertCss(css) {\n  if (!css || !IS_DOM) {\n    return;\n  }\n  const style = DOCUMENT.createElement('style');\n  style.setAttribute('type', 'text/css');\n  style.innerHTML = css;\n  const headChildren = DOCUMENT.head.childNodes;\n  let beforeChild = null;\n  for (let i = headChildren.length - 1; i > -1; i--) {\n    const child = headChildren[i];\n    const tagName = (child.tagName || '').toUpperCase();\n    if (['STYLE', 'LINK'].indexOf(tagName) > -1) {\n      beforeChild = child;\n    }\n  }\n  DOCUMENT.head.insertBefore(style, beforeChild);\n  return css;\n}\nconst idPool = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';\nfunction nextUniqueId() {\n  let size = 12;\n  let id = '';\n  while (size-- > 0) {\n    id += idPool[Math.random() * 62 | 0];\n  }\n  return id;\n}\nfunction toArray(obj) {\n  const array = [];\n  for (let i = (obj || []).length >>> 0; i--;) {\n    array[i] = obj[i];\n  }\n  return array;\n}\nfunction classArray(node) {\n  if (node.classList) {\n    return toArray(node.classList);\n  } else {\n    return (node.getAttribute('class') || '').split(' ').filter(i => i);\n  }\n}\nfunction htmlEscape(str) {\n  return \"\".concat(str).replace(/&/g, '&amp;').replace(/\"/g, '&quot;').replace(/'/g, '&#39;').replace(/</g, '&lt;').replace(/>/g, '&gt;');\n}\nfunction joinAttributes(attributes) {\n  return Object.keys(attributes || {}).reduce((acc, attributeName) => {\n    return acc + \"\".concat(attributeName, \"=\\\"\").concat(htmlEscape(attributes[attributeName]), \"\\\" \");\n  }, '').trim();\n}\nfunction joinStyles(styles) {\n  return Object.keys(styles || {}).reduce((acc, styleName) => {\n    return acc + \"\".concat(styleName, \": \").concat(styles[styleName].trim(), \";\");\n  }, '');\n}\nfunction transformIsMeaningful(transform) {\n  return transform.size !== meaninglessTransform.size || transform.x !== meaninglessTransform.x || transform.y !== meaninglessTransform.y || transform.rotate !== meaninglessTransform.rotate || transform.flipX || transform.flipY;\n}\nfunction transformForSvg(_ref) {\n  let {\n    transform,\n    containerWidth,\n    iconWidth\n  } = _ref;\n  const outer = {\n    transform: \"translate(\".concat(containerWidth / 2, \" 256)\")\n  };\n  const innerTranslate = \"translate(\".concat(transform.x * 32, \", \").concat(transform.y * 32, \") \");\n  const innerScale = \"scale(\".concat(transform.size / 16 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / 16 * (transform.flipY ? -1 : 1), \") \");\n  const innerRotate = \"rotate(\".concat(transform.rotate, \" 0 0)\");\n  const inner = {\n    transform: \"\".concat(innerTranslate, \" \").concat(innerScale, \" \").concat(innerRotate)\n  };\n  const path = {\n    transform: \"translate(\".concat(iconWidth / 2 * -1, \" -256)\")\n  };\n  return {\n    outer,\n    inner,\n    path\n  };\n}\nfunction transformForCss(_ref2) {\n  let {\n    transform,\n    width = UNITS_IN_GRID,\n    height = UNITS_IN_GRID,\n    startCentered = false\n  } = _ref2;\n  let val = '';\n  if (startCentered && IS_IE) {\n    val += \"translate(\".concat(transform.x / d$2 - width / 2, \"em, \").concat(transform.y / d$2 - height / 2, \"em) \");\n  } else if (startCentered) {\n    val += \"translate(calc(-50% + \".concat(transform.x / d$2, \"em), calc(-50% + \").concat(transform.y / d$2, \"em)) \");\n  } else {\n    val += \"translate(\".concat(transform.x / d$2, \"em, \").concat(transform.y / d$2, \"em) \");\n  }\n  val += \"scale(\".concat(transform.size / d$2 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / d$2 * (transform.flipY ? -1 : 1), \") \");\n  val += \"rotate(\".concat(transform.rotate, \"deg) \");\n  return val;\n}\n\nvar baseStyles = \":root, :host {\\n  --fa-font-solid: normal 900 1em/1 \\\"Font Awesome 6 Free\\\";\\n  --fa-font-regular: normal 400 1em/1 \\\"Font Awesome 6 Free\\\";\\n  --fa-font-light: normal 300 1em/1 \\\"Font Awesome 6 Pro\\\";\\n  --fa-font-thin: normal 100 1em/1 \\\"Font Awesome 6 Pro\\\";\\n  --fa-font-duotone: normal 900 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-duotone-regular: normal 400 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-duotone-light: normal 300 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-duotone-thin: normal 100 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-brands: normal 400 1em/1 \\\"Font Awesome 6 Brands\\\";\\n  --fa-font-sharp-solid: normal 900 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-regular: normal 400 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-light: normal 300 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-thin: normal 100 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-duotone-solid: normal 900 1em/1 \\\"Font Awesome 6 Sharp Duotone\\\";\\n  --fa-font-sharp-duotone-regular: normal 400 1em/1 \\\"Font Awesome 6 Sharp Duotone\\\";\\n  --fa-font-sharp-duotone-light: normal 300 1em/1 \\\"Font Awesome 6 Sharp Duotone\\\";\\n  --fa-font-sharp-duotone-thin: normal 100 1em/1 \\\"Font Awesome 6 Sharp Duotone\\\";\\n}\\n\\nsvg:not(:root).svg-inline--fa, svg:not(:host).svg-inline--fa {\\n  overflow: visible;\\n  box-sizing: content-box;\\n}\\n\\n.svg-inline--fa {\\n  display: var(--fa-display, inline-block);\\n  height: 1em;\\n  overflow: visible;\\n  vertical-align: -0.125em;\\n}\\n.svg-inline--fa.fa-2xs {\\n  vertical-align: 0.1em;\\n}\\n.svg-inline--fa.fa-xs {\\n  vertical-align: 0em;\\n}\\n.svg-inline--fa.fa-sm {\\n  vertical-align: -0.0714285705em;\\n}\\n.svg-inline--fa.fa-lg {\\n  vertical-align: -0.2em;\\n}\\n.svg-inline--fa.fa-xl {\\n  vertical-align: -0.25em;\\n}\\n.svg-inline--fa.fa-2xl {\\n  vertical-align: -0.3125em;\\n}\\n.svg-inline--fa.fa-pull-left {\\n  margin-right: var(--fa-pull-margin, 0.3em);\\n  width: auto;\\n}\\n.svg-inline--fa.fa-pull-right {\\n  margin-left: var(--fa-pull-margin, 0.3em);\\n  width: auto;\\n}\\n.svg-inline--fa.fa-li {\\n  width: var(--fa-li-width, 2em);\\n  top: 0.25em;\\n}\\n.svg-inline--fa.fa-fw {\\n  width: var(--fa-fw-width, 1.25em);\\n}\\n\\n.fa-layers svg.svg-inline--fa {\\n  bottom: 0;\\n  left: 0;\\n  margin: auto;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n}\\n\\n.fa-layers-counter, .fa-layers-text {\\n  display: inline-block;\\n  position: absolute;\\n  text-align: center;\\n}\\n\\n.fa-layers {\\n  display: inline-block;\\n  height: 1em;\\n  position: relative;\\n  text-align: center;\\n  vertical-align: -0.125em;\\n  width: 1em;\\n}\\n.fa-layers svg.svg-inline--fa {\\n  transform-origin: center center;\\n}\\n\\n.fa-layers-text {\\n  left: 50%;\\n  top: 50%;\\n  transform: translate(-50%, -50%);\\n  transform-origin: center center;\\n}\\n\\n.fa-layers-counter {\\n  background-color: var(--fa-counter-background-color, #ff253a);\\n  border-radius: var(--fa-counter-border-radius, 1em);\\n  box-sizing: border-box;\\n  color: var(--fa-inverse, #fff);\\n  line-height: var(--fa-counter-line-height, 1);\\n  max-width: var(--fa-counter-max-width, 5em);\\n  min-width: var(--fa-counter-min-width, 1.5em);\\n  overflow: hidden;\\n  padding: var(--fa-counter-padding, 0.25em 0.5em);\\n  right: var(--fa-right, 0);\\n  text-overflow: ellipsis;\\n  top: var(--fa-top, 0);\\n  transform: scale(var(--fa-counter-scale, 0.25));\\n  transform-origin: top right;\\n}\\n\\n.fa-layers-bottom-right {\\n  bottom: var(--fa-bottom, 0);\\n  right: var(--fa-right, 0);\\n  top: auto;\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: bottom right;\\n}\\n\\n.fa-layers-bottom-left {\\n  bottom: var(--fa-bottom, 0);\\n  left: var(--fa-left, 0);\\n  right: auto;\\n  top: auto;\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: bottom left;\\n}\\n\\n.fa-layers-top-right {\\n  top: var(--fa-top, 0);\\n  right: var(--fa-right, 0);\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: top right;\\n}\\n\\n.fa-layers-top-left {\\n  left: var(--fa-left, 0);\\n  right: auto;\\n  top: var(--fa-top, 0);\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: top left;\\n}\\n\\n.fa-1x {\\n  font-size: 1em;\\n}\\n\\n.fa-2x {\\n  font-size: 2em;\\n}\\n\\n.fa-3x {\\n  font-size: 3em;\\n}\\n\\n.fa-4x {\\n  font-size: 4em;\\n}\\n\\n.fa-5x {\\n  font-size: 5em;\\n}\\n\\n.fa-6x {\\n  font-size: 6em;\\n}\\n\\n.fa-7x {\\n  font-size: 7em;\\n}\\n\\n.fa-8x {\\n  font-size: 8em;\\n}\\n\\n.fa-9x {\\n  font-size: 9em;\\n}\\n\\n.fa-10x {\\n  font-size: 10em;\\n}\\n\\n.fa-2xs {\\n  font-size: 0.625em;\\n  line-height: 0.1em;\\n  vertical-align: 0.225em;\\n}\\n\\n.fa-xs {\\n  font-size: 0.75em;\\n  line-height: 0.0833333337em;\\n  vertical-align: 0.125em;\\n}\\n\\n.fa-sm {\\n  font-size: 0.875em;\\n  line-height: 0.0714285718em;\\n  vertical-align: 0.0535714295em;\\n}\\n\\n.fa-lg {\\n  font-size: 1.25em;\\n  line-height: 0.05em;\\n  vertical-align: -0.075em;\\n}\\n\\n.fa-xl {\\n  font-size: 1.5em;\\n  line-height: 0.0416666682em;\\n  vertical-align: -0.125em;\\n}\\n\\n.fa-2xl {\\n  font-size: 2em;\\n  line-height: 0.03125em;\\n  vertical-align: -0.1875em;\\n}\\n\\n.fa-fw {\\n  text-align: center;\\n  width: 1.25em;\\n}\\n\\n.fa-ul {\\n  list-style-type: none;\\n  margin-left: var(--fa-li-margin, 2.5em);\\n  padding-left: 0;\\n}\\n.fa-ul > li {\\n  position: relative;\\n}\\n\\n.fa-li {\\n  left: calc(-1 * var(--fa-li-width, 2em));\\n  position: absolute;\\n  text-align: center;\\n  width: var(--fa-li-width, 2em);\\n  line-height: inherit;\\n}\\n\\n.fa-border {\\n  border-color: var(--fa-border-color, #eee);\\n  border-radius: var(--fa-border-radius, 0.1em);\\n  border-style: var(--fa-border-style, solid);\\n  border-width: var(--fa-border-width, 0.08em);\\n  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em);\\n}\\n\\n.fa-pull-left {\\n  float: left;\\n  margin-right: var(--fa-pull-margin, 0.3em);\\n}\\n\\n.fa-pull-right {\\n  float: right;\\n  margin-left: var(--fa-pull-margin, 0.3em);\\n}\\n\\n.fa-beat {\\n  animation-name: fa-beat;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n}\\n\\n.fa-bounce {\\n  animation-name: fa-bounce;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\\n}\\n\\n.fa-fade {\\n  animation-name: fa-fade;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n}\\n\\n.fa-beat-fade {\\n  animation-name: fa-beat-fade;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n}\\n\\n.fa-flip {\\n  animation-name: fa-flip;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n}\\n\\n.fa-shake {\\n  animation-name: fa-shake;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, linear);\\n}\\n\\n.fa-spin {\\n  animation-name: fa-spin;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 2s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, linear);\\n}\\n\\n.fa-spin-reverse {\\n  --fa-animation-direction: reverse;\\n}\\n\\n.fa-pulse,\\n.fa-spin-pulse {\\n  animation-name: fa-spin;\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, steps(8));\\n}\\n\\n@media (prefers-reduced-motion: reduce) {\\n  .fa-beat,\\n.fa-bounce,\\n.fa-fade,\\n.fa-beat-fade,\\n.fa-flip,\\n.fa-pulse,\\n.fa-shake,\\n.fa-spin,\\n.fa-spin-pulse {\\n    animation-delay: -1ms;\\n    animation-duration: 1ms;\\n    animation-iteration-count: 1;\\n    transition-delay: 0s;\\n    transition-duration: 0s;\\n  }\\n}\\n@keyframes fa-beat {\\n  0%, 90% {\\n    transform: scale(1);\\n  }\\n  45% {\\n    transform: scale(var(--fa-beat-scale, 1.25));\\n  }\\n}\\n@keyframes fa-bounce {\\n  0% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n  10% {\\n    transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\\n  }\\n  30% {\\n    transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\\n  }\\n  50% {\\n    transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\\n  }\\n  57% {\\n    transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\\n  }\\n  64% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n  100% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n}\\n@keyframes fa-fade {\\n  50% {\\n    opacity: var(--fa-fade-opacity, 0.4);\\n  }\\n}\\n@keyframes fa-beat-fade {\\n  0%, 100% {\\n    opacity: var(--fa-beat-fade-opacity, 0.4);\\n    transform: scale(1);\\n  }\\n  50% {\\n    opacity: 1;\\n    transform: scale(var(--fa-beat-fade-scale, 1.125));\\n  }\\n}\\n@keyframes fa-flip {\\n  50% {\\n    transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\\n  }\\n}\\n@keyframes fa-shake {\\n  0% {\\n    transform: rotate(-15deg);\\n  }\\n  4% {\\n    transform: rotate(15deg);\\n  }\\n  8%, 24% {\\n    transform: rotate(-18deg);\\n  }\\n  12%, 28% {\\n    transform: rotate(18deg);\\n  }\\n  16% {\\n    transform: rotate(-22deg);\\n  }\\n  20% {\\n    transform: rotate(22deg);\\n  }\\n  32% {\\n    transform: rotate(-12deg);\\n  }\\n  36% {\\n    transform: rotate(12deg);\\n  }\\n  40%, 100% {\\n    transform: rotate(0deg);\\n  }\\n}\\n@keyframes fa-spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.fa-rotate-90 {\\n  transform: rotate(90deg);\\n}\\n\\n.fa-rotate-180 {\\n  transform: rotate(180deg);\\n}\\n\\n.fa-rotate-270 {\\n  transform: rotate(270deg);\\n}\\n\\n.fa-flip-horizontal {\\n  transform: scale(-1, 1);\\n}\\n\\n.fa-flip-vertical {\\n  transform: scale(1, -1);\\n}\\n\\n.fa-flip-both,\\n.fa-flip-horizontal.fa-flip-vertical {\\n  transform: scale(-1, -1);\\n}\\n\\n.fa-rotate-by {\\n  transform: rotate(var(--fa-rotate-angle, 0));\\n}\\n\\n.fa-stack {\\n  display: inline-block;\\n  vertical-align: middle;\\n  height: 2em;\\n  position: relative;\\n  width: 2.5em;\\n}\\n\\n.fa-stack-1x,\\n.fa-stack-2x {\\n  bottom: 0;\\n  left: 0;\\n  margin: auto;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n  z-index: var(--fa-stack-z-index, auto);\\n}\\n\\n.svg-inline--fa.fa-stack-1x {\\n  height: 1em;\\n  width: 1.25em;\\n}\\n.svg-inline--fa.fa-stack-2x {\\n  height: 2em;\\n  width: 2.5em;\\n}\\n\\n.fa-inverse {\\n  color: var(--fa-inverse, #fff);\\n}\\n\\n.sr-only,\\n.fa-sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n\\n.sr-only-focusable:not(:focus),\\n.fa-sr-only-focusable:not(:focus) {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n\\n.svg-inline--fa .fa-primary {\\n  fill: var(--fa-primary-color, currentColor);\\n  opacity: var(--fa-primary-opacity, 1);\\n}\\n\\n.svg-inline--fa .fa-secondary {\\n  fill: var(--fa-secondary-color, currentColor);\\n  opacity: var(--fa-secondary-opacity, 0.4);\\n}\\n\\n.svg-inline--fa.fa-swap-opacity .fa-primary {\\n  opacity: var(--fa-secondary-opacity, 0.4);\\n}\\n\\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\\n  opacity: var(--fa-primary-opacity, 1);\\n}\\n\\n.svg-inline--fa mask .fa-primary,\\n.svg-inline--fa mask .fa-secondary {\\n  fill: black;\\n}\";\n\nfunction css() {\n  const dcp = DEFAULT_CSS_PREFIX;\n  const drc = DEFAULT_REPLACEMENT_CLASS;\n  const fp = config.cssPrefix;\n  const rc = config.replacementClass;\n  let s = baseStyles;\n  if (fp !== dcp || rc !== drc) {\n    const dPatt = new RegExp(\"\\\\.\".concat(dcp, \"\\\\-\"), 'g');\n    const customPropPatt = new RegExp(\"\\\\--\".concat(dcp, \"\\\\-\"), 'g');\n    const rPatt = new RegExp(\"\\\\.\".concat(drc), 'g');\n    s = s.replace(dPatt, \".\".concat(fp, \"-\")).replace(customPropPatt, \"--\".concat(fp, \"-\")).replace(rPatt, \".\".concat(rc));\n  }\n  return s;\n}\nlet _cssInserted = false;\nfunction ensureCss() {\n  if (config.autoAddCss && !_cssInserted) {\n    insertCss(css());\n    _cssInserted = true;\n  }\n}\nvar InjectCSS = {\n  mixout() {\n    return {\n      dom: {\n        css,\n        insertCss: ensureCss\n      }\n    };\n  },\n  hooks() {\n    return {\n      beforeDOMElementCreation() {\n        ensureCss();\n      },\n      beforeI2svg() {\n        ensureCss();\n      }\n    };\n  }\n};\n\nconst w = WINDOW || {};\nif (!w[NAMESPACE_IDENTIFIER]) w[NAMESPACE_IDENTIFIER] = {};\nif (!w[NAMESPACE_IDENTIFIER].styles) w[NAMESPACE_IDENTIFIER].styles = {};\nif (!w[NAMESPACE_IDENTIFIER].hooks) w[NAMESPACE_IDENTIFIER].hooks = {};\nif (!w[NAMESPACE_IDENTIFIER].shims) w[NAMESPACE_IDENTIFIER].shims = [];\nvar namespace = w[NAMESPACE_IDENTIFIER];\n\nconst functions = [];\nconst listener = function () {\n  DOCUMENT.removeEventListener('DOMContentLoaded', listener);\n  loaded = 1;\n  functions.map(fn => fn());\n};\nlet loaded = false;\nif (IS_DOM) {\n  loaded = (DOCUMENT.documentElement.doScroll ? /^loaded|^c/ : /^loaded|^i|^c/).test(DOCUMENT.readyState);\n  if (!loaded) DOCUMENT.addEventListener('DOMContentLoaded', listener);\n}\nfunction domready (fn) {\n  if (!IS_DOM) return;\n  loaded ? setTimeout(fn, 0) : functions.push(fn);\n}\n\nfunction toHtml(abstractNodes) {\n  const {\n    tag,\n    attributes = {},\n    children = []\n  } = abstractNodes;\n  if (typeof abstractNodes === 'string') {\n    return htmlEscape(abstractNodes);\n  } else {\n    return \"<\".concat(tag, \" \").concat(joinAttributes(attributes), \">\").concat(children.map(toHtml).join(''), \"</\").concat(tag, \">\");\n  }\n}\n\nfunction iconFromMapping(mapping, prefix, iconName) {\n  if (mapping && mapping[prefix] && mapping[prefix][iconName]) {\n    return {\n      prefix,\n      iconName,\n      icon: mapping[prefix][iconName]\n    };\n  }\n}\n\n/**\n * Internal helper to bind a function known to have 4 arguments\n * to a given context.\n */\nvar bindInternal4 = function bindInternal4(func, thisContext) {\n  return function (a, b, c, d) {\n    return func.call(thisContext, a, b, c, d);\n  };\n};\n\n/**\n * # Reduce\n *\n * A fast object `.reduce()` implementation.\n *\n * @param  {Object}   subject      The object to reduce over.\n * @param  {Function} fn           The reducer function.\n * @param  {mixed}    initialValue The initial value for the reducer, defaults to subject[0].\n * @param  {Object}   thisContext  The context for the reducer.\n * @return {mixed}                 The final result.\n */\nvar reduce = function fastReduceObject(subject, fn, initialValue, thisContext) {\n  var keys = Object.keys(subject),\n    length = keys.length,\n    iterator = thisContext !== undefined ? bindInternal4(fn, thisContext) : fn,\n    i,\n    key,\n    result;\n  if (initialValue === undefined) {\n    i = 1;\n    result = subject[keys[0]];\n  } else {\n    i = 0;\n    result = initialValue;\n  }\n  for (; i < length; i++) {\n    key = keys[i];\n    result = iterator(result, subject[key], key, subject);\n  }\n  return result;\n};\n\n/**\n * ucs2decode() and codePointAt() are both works of Mathias Bynens and licensed under MIT\n *\n * Copyright Mathias Bynens <https://mathiasbynens.be/>\n\n * Permission is hereby granted, free of charge, to any person obtaining\n * a copy of this software and associated documentation files (the\n * \"Software\"), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to\n * permit persons to whom the Software is furnished to do so, subject to\n * the following conditions:\n\n * The above copyright notice and this permission notice shall be\n * included in all copies or substantial portions of the Software.\n\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\n * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\n * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\n * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n\nfunction ucs2decode(string) {\n  const output = [];\n  let counter = 0;\n  const length = string.length;\n  while (counter < length) {\n    const value = string.charCodeAt(counter++);\n    if (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n      const extra = string.charCodeAt(counter++);\n      if ((extra & 0xFC00) == 0xDC00) {\n        // eslint-disable-line eqeqeq\n        output.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n      } else {\n        output.push(value);\n        counter--;\n      }\n    } else {\n      output.push(value);\n    }\n  }\n  return output;\n}\nfunction toHex(unicode) {\n  const decoded = ucs2decode(unicode);\n  return decoded.length === 1 ? decoded[0].toString(16) : null;\n}\nfunction codePointAt(string, index) {\n  const size = string.length;\n  let first = string.charCodeAt(index);\n  let second;\n  if (first >= 0xD800 && first <= 0xDBFF && size > index + 1) {\n    second = string.charCodeAt(index + 1);\n    if (second >= 0xDC00 && second <= 0xDFFF) {\n      return (first - 0xD800) * 0x400 + second - 0xDC00 + 0x10000;\n    }\n  }\n  return first;\n}\n\nfunction normalizeIcons(icons) {\n  return Object.keys(icons).reduce((acc, iconName) => {\n    const icon = icons[iconName];\n    const expanded = !!icon.icon;\n    if (expanded) {\n      acc[icon.iconName] = icon.icon;\n    } else {\n      acc[iconName] = icon;\n    }\n    return acc;\n  }, {});\n}\nfunction defineIcons(prefix, icons) {\n  let params = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  const {\n    skipHooks = false\n  } = params;\n  const normalized = normalizeIcons(icons);\n  if (typeof namespace.hooks.addPack === 'function' && !skipHooks) {\n    namespace.hooks.addPack(prefix, normalizeIcons(icons));\n  } else {\n    namespace.styles[prefix] = _objectSpread2(_objectSpread2({}, namespace.styles[prefix] || {}), normalized);\n  }\n\n  /**\n   * Font Awesome 4 used the prefix of `fa` for all icons. With the introduction\n   * of new styles we needed to differentiate between them. Prefix `fa` is now an alias\n   * for `fas` so we'll ease the upgrade process for our users by automatically defining\n   * this as well.\n   */\n  if (prefix === 'fas') {\n    defineIcons('fa', icons);\n  }\n}\n\nconst duotonePathRe = [/*#__PURE__*/_wrapRegExp(/path d=\"([^\"]+)\".*path d=\"([^\"]+)\"/, {\n  d1: 1,\n  d2: 2\n}), /*#__PURE__*/_wrapRegExp(/path class=\"([^\"]+)\".*d=\"([^\"]+)\".*path class=\"([^\"]+)\".*d=\"([^\"]+)\"/, {\n  cls1: 1,\n  d1: 2,\n  cls2: 3,\n  d2: 4\n}), /*#__PURE__*/_wrapRegExp(/path class=\"([^\"]+)\".*d=\"([^\"]+)\"/, {\n  cls1: 1,\n  d1: 2\n})];\n\nconst {\n  styles,\n  shims\n} = namespace;\nconst FAMILY_NAMES = Object.keys(PREFIX_TO_LONG_STYLE);\nconst PREFIXES_FOR_FAMILY = FAMILY_NAMES.reduce((acc, familyId) => {\n  acc[familyId] = Object.keys(PREFIX_TO_LONG_STYLE[familyId]);\n  return acc;\n}, {});\nlet _defaultUsablePrefix = null;\nlet _byUnicode = {};\nlet _byLigature = {};\nlet _byOldName = {};\nlet _byOldUnicode = {};\nlet _byAlias = {};\nfunction isReserved(name) {\n  return ~RESERVED_CLASSES.indexOf(name);\n}\nfunction getIconName(cssPrefix, cls) {\n  const parts = cls.split('-');\n  const prefix = parts[0];\n  const iconName = parts.slice(1).join('-');\n  if (prefix === cssPrefix && iconName !== '' && !isReserved(iconName)) {\n    return iconName;\n  } else {\n    return null;\n  }\n}\nconst build = () => {\n  const lookup = reducer => {\n    return reduce(styles, (o$$1, style, prefix) => {\n      o$$1[prefix] = reduce(style, reducer, {});\n      return o$$1;\n    }, {});\n  };\n  _byUnicode = lookup((acc, icon, iconName) => {\n    if (icon[3]) {\n      acc[icon[3]] = iconName;\n    }\n    if (icon[2]) {\n      const aliases = icon[2].filter(a$$1 => {\n        return typeof a$$1 === 'number';\n      });\n      aliases.forEach(alias => {\n        acc[alias.toString(16)] = iconName;\n      });\n    }\n    return acc;\n  });\n  _byLigature = lookup((acc, icon, iconName) => {\n    acc[iconName] = iconName;\n    if (icon[2]) {\n      const aliases = icon[2].filter(a$$1 => {\n        return typeof a$$1 === 'string';\n      });\n      aliases.forEach(alias => {\n        acc[alias] = iconName;\n      });\n    }\n    return acc;\n  });\n  _byAlias = lookup((acc, icon, iconName) => {\n    const aliases = icon[2];\n    acc[iconName] = iconName;\n    aliases.forEach(alias => {\n      acc[alias] = iconName;\n    });\n    return acc;\n  });\n\n  // If we have a Kit, we can't determine if regular is available since we\n  // could be auto-fetching it. We'll have to assume that it is available.\n  const hasRegular = 'far' in styles || config.autoFetchSvg;\n  const shimLookups = reduce(shims, (acc, shim) => {\n    const maybeNameMaybeUnicode = shim[0];\n    let prefix = shim[1];\n    const iconName = shim[2];\n    if (prefix === 'far' && !hasRegular) {\n      prefix = 'fas';\n    }\n    if (typeof maybeNameMaybeUnicode === 'string') {\n      acc.names[maybeNameMaybeUnicode] = {\n        prefix,\n        iconName\n      };\n    }\n    if (typeof maybeNameMaybeUnicode === 'number') {\n      acc.unicodes[maybeNameMaybeUnicode.toString(16)] = {\n        prefix,\n        iconName\n      };\n    }\n    return acc;\n  }, {\n    names: {},\n    unicodes: {}\n  });\n  _byOldName = shimLookups.names;\n  _byOldUnicode = shimLookups.unicodes;\n  _defaultUsablePrefix = getCanonicalPrefix(config.styleDefault, {\n    family: config.familyDefault\n  });\n};\nonChange(c$$1 => {\n  _defaultUsablePrefix = getCanonicalPrefix(c$$1.styleDefault, {\n    family: config.familyDefault\n  });\n});\nbuild();\nfunction byUnicode(prefix, unicode) {\n  return (_byUnicode[prefix] || {})[unicode];\n}\nfunction byLigature(prefix, ligature) {\n  return (_byLigature[prefix] || {})[ligature];\n}\nfunction byAlias(prefix, alias) {\n  return (_byAlias[prefix] || {})[alias];\n}\nfunction byOldName(name) {\n  return _byOldName[name] || {\n    prefix: null,\n    iconName: null\n  };\n}\nfunction byOldUnicode(unicode) {\n  const oldUnicode = _byOldUnicode[unicode];\n  const newUnicode = byUnicode('fas', unicode);\n  return oldUnicode || (newUnicode ? {\n    prefix: 'fas',\n    iconName: newUnicode\n  } : null) || {\n    prefix: null,\n    iconName: null\n  };\n}\nfunction getDefaultUsablePrefix() {\n  return _defaultUsablePrefix;\n}\nconst emptyCanonicalIcon = () => {\n  return {\n    prefix: null,\n    iconName: null,\n    rest: []\n  };\n};\nfunction getFamilyId(values) {\n  let family = s;\n  const famProps = FAMILY_NAMES.reduce((acc, familyId) => {\n    acc[familyId] = \"\".concat(config.cssPrefix, \"-\").concat(familyId);\n    return acc;\n  }, {});\n  L.forEach(familyId => {\n    if (values.includes(famProps[familyId]) || values.some(v$$1 => PREFIXES_FOR_FAMILY[familyId].includes(v$$1))) {\n      family = familyId;\n    }\n  });\n  return family;\n}\nfunction getCanonicalPrefix(styleOrPrefix) {\n  let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    family = s\n  } = params;\n  const style = PREFIX_TO_STYLE[family][styleOrPrefix];\n\n  // handles the exception of passing in only a family of 'duotone' with no style\n  if (family === t && !styleOrPrefix) {\n    return 'fad';\n  }\n  const prefix = STYLE_TO_PREFIX[family][styleOrPrefix] || STYLE_TO_PREFIX[family][style];\n  const defined = styleOrPrefix in namespace.styles ? styleOrPrefix : null;\n  const result = prefix || defined || null;\n  return result;\n}\nfunction moveNonFaClassesToRest(classNames) {\n  let rest = [];\n  let iconName = null;\n  classNames.forEach(cls => {\n    const result = getIconName(config.cssPrefix, cls);\n    if (result) {\n      iconName = result;\n    } else if (cls) {\n      rest.push(cls);\n    }\n  });\n  return {\n    iconName,\n    rest\n  };\n}\nfunction sortedUniqueValues(arr) {\n  return arr.sort().filter((value, index, arr) => {\n    return arr.indexOf(value) === index;\n  });\n}\nfunction getCanonicalIcon(values) {\n  let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    skipLookups = false\n  } = params;\n  let givenPrefix = null;\n  const faCombinedClasses = Ia.concat(bt$1);\n  const faStyleOrFamilyClasses = sortedUniqueValues(values.filter(cls => faCombinedClasses.includes(cls)));\n  const nonStyleOrFamilyClasses = sortedUniqueValues(values.filter(cls => !Ia.includes(cls)));\n  const faStyles = faStyleOrFamilyClasses.filter(cls => {\n    givenPrefix = cls;\n    return !P.includes(cls);\n  });\n  const [styleFromValues = null] = faStyles;\n  const family = getFamilyId(faStyleOrFamilyClasses);\n  const canonical = _objectSpread2(_objectSpread2({}, moveNonFaClassesToRest(nonStyleOrFamilyClasses)), {}, {\n    prefix: getCanonicalPrefix(styleFromValues, {\n      family\n    })\n  });\n  return _objectSpread2(_objectSpread2(_objectSpread2({}, canonical), getDefaultCanonicalPrefix({\n    values,\n    family,\n    styles,\n    config,\n    canonical,\n    givenPrefix\n  })), applyShimAndAlias(skipLookups, givenPrefix, canonical));\n}\nfunction applyShimAndAlias(skipLookups, givenPrefix, canonical) {\n  let {\n    prefix,\n    iconName\n  } = canonical;\n  if (skipLookups || !prefix || !iconName) {\n    return {\n      prefix,\n      iconName\n    };\n  }\n  const shim = givenPrefix === 'fa' ? byOldName(iconName) : {};\n  const aliasIconName = byAlias(prefix, iconName);\n  iconName = shim.iconName || aliasIconName || iconName;\n  prefix = shim.prefix || prefix;\n  if (prefix === 'far' && !styles['far'] && styles['fas'] && !config.autoFetchSvg) {\n    // Allow a fallback from the regular style to solid if regular is not available\n    // but only if we aren't auto-fetching SVGs\n    prefix = 'fas';\n  }\n  return {\n    prefix,\n    iconName\n  };\n}\nconst newCanonicalFamilies = L.filter(familyId => {\n  return familyId !== s || familyId !== t;\n});\nconst newCanonicalStyles = Object.keys(ga).filter(key => key !== s).map(key => Object.keys(ga[key])).flat();\nfunction getDefaultCanonicalPrefix(prefixOptions) {\n  const {\n    values,\n    family,\n    canonical,\n    givenPrefix = '',\n    styles = {},\n    config: config$$1 = {}\n  } = prefixOptions;\n  const isDuotoneFamily = family === t;\n  const valuesHasDuotone = values.includes('fa-duotone') || values.includes('fad');\n  const defaultFamilyIsDuotone = config$$1.familyDefault === 'duotone';\n  const canonicalPrefixIsDuotone = canonical.prefix === 'fad' || canonical.prefix === 'fa-duotone';\n  if (!isDuotoneFamily && (valuesHasDuotone || defaultFamilyIsDuotone || canonicalPrefixIsDuotone)) {\n    canonical.prefix = 'fad';\n  }\n  if (values.includes('fa-brands') || values.includes('fab')) {\n    canonical.prefix = 'fab';\n  }\n  if (!canonical.prefix && newCanonicalFamilies.includes(family)) {\n    const validPrefix = Object.keys(styles).find(key => newCanonicalStyles.includes(key));\n    if (validPrefix || config$$1.autoFetchSvg) {\n      const defaultPrefix = pt.get(family).defaultShortPrefixId;\n      canonical.prefix = defaultPrefix;\n      canonical.iconName = byAlias(canonical.prefix, canonical.iconName) || canonical.iconName;\n    }\n  }\n  if (canonical.prefix === 'fa' || givenPrefix === 'fa') {\n    // The fa prefix is not canonical. So if it has made it through until this point\n    // we will shift it to the correct prefix.\n    canonical.prefix = getDefaultUsablePrefix() || 'fas';\n  }\n  return canonical;\n}\n\nclass Library {\n  constructor() {\n    this.definitions = {};\n  }\n  add() {\n    for (var _len = arguments.length, definitions = new Array(_len), _key = 0; _key < _len; _key++) {\n      definitions[_key] = arguments[_key];\n    }\n    const additions = definitions.reduce(this._pullDefinitions, {});\n    Object.keys(additions).forEach(key => {\n      this.definitions[key] = _objectSpread2(_objectSpread2({}, this.definitions[key] || {}), additions[key]);\n      defineIcons(key, additions[key]);\n\n      // TODO can we stop doing this? We can't get the icons by 'fa-solid' any longer so this probably needs to change\n      const longPrefix = PREFIX_TO_LONG_STYLE[s][key];\n      if (longPrefix) defineIcons(longPrefix, additions[key]);\n      build();\n    });\n  }\n  reset() {\n    this.definitions = {};\n  }\n  _pullDefinitions(additions, definition) {\n    const normalized = definition.prefix && definition.iconName && definition.icon ? {\n      0: definition\n    } : definition;\n    Object.keys(normalized).map(key => {\n      const {\n        prefix,\n        iconName,\n        icon\n      } = normalized[key];\n      const aliases = icon[2];\n      if (!additions[prefix]) additions[prefix] = {};\n      if (aliases.length > 0) {\n        aliases.forEach(alias => {\n          if (typeof alias === 'string') {\n            additions[prefix][alias] = icon;\n          }\n        });\n      }\n      additions[prefix][iconName] = icon;\n    });\n    return additions;\n  }\n}\n\nlet _plugins = [];\nlet _hooks = {};\nconst providers = {};\nconst defaultProviderKeys = Object.keys(providers);\nfunction registerPlugins(nextPlugins, _ref) {\n  let {\n    mixoutsTo: obj\n  } = _ref;\n  _plugins = nextPlugins;\n  _hooks = {};\n  Object.keys(providers).forEach(k => {\n    if (defaultProviderKeys.indexOf(k) === -1) {\n      delete providers[k];\n    }\n  });\n  _plugins.forEach(plugin => {\n    const mixout = plugin.mixout ? plugin.mixout() : {};\n    Object.keys(mixout).forEach(tk => {\n      if (typeof mixout[tk] === 'function') {\n        obj[tk] = mixout[tk];\n      }\n      if (typeof mixout[tk] === 'object') {\n        Object.keys(mixout[tk]).forEach(sk => {\n          if (!obj[tk]) {\n            obj[tk] = {};\n          }\n          obj[tk][sk] = mixout[tk][sk];\n        });\n      }\n    });\n    if (plugin.hooks) {\n      const hooks = plugin.hooks();\n      Object.keys(hooks).forEach(hook => {\n        if (!_hooks[hook]) {\n          _hooks[hook] = [];\n        }\n        _hooks[hook].push(hooks[hook]);\n      });\n    }\n    if (plugin.provides) {\n      plugin.provides(providers);\n    }\n  });\n  return obj;\n}\nfunction chainHooks(hook, accumulator) {\n  for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    args[_key - 2] = arguments[_key];\n  }\n  const hookFns = _hooks[hook] || [];\n  hookFns.forEach(hookFn => {\n    accumulator = hookFn.apply(null, [accumulator, ...args]); // eslint-disable-line no-useless-call\n  });\n  return accumulator;\n}\nfunction callHooks(hook) {\n  for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    args[_key2 - 1] = arguments[_key2];\n  }\n  const hookFns = _hooks[hook] || [];\n  hookFns.forEach(hookFn => {\n    hookFn.apply(null, args);\n  });\n  return undefined;\n}\nfunction callProvided() {\n  const hook = arguments[0];\n  const args = Array.prototype.slice.call(arguments, 1);\n  return providers[hook] ? providers[hook].apply(null, args) : undefined;\n}\n\nfunction findIconDefinition(iconLookup) {\n  if (iconLookup.prefix === 'fa') {\n    iconLookup.prefix = 'fas';\n  }\n  let {\n    iconName\n  } = iconLookup;\n  const prefix = iconLookup.prefix || getDefaultUsablePrefix();\n  if (!iconName) return;\n  iconName = byAlias(prefix, iconName) || iconName;\n  return iconFromMapping(library.definitions, prefix, iconName) || iconFromMapping(namespace.styles, prefix, iconName);\n}\nconst library = new Library();\nconst noAuto = () => {\n  config.autoReplaceSvg = false;\n  config.observeMutations = false;\n  callHooks('noAuto');\n};\nconst dom = {\n  i2svg: function () {\n    let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (IS_DOM) {\n      callHooks('beforeI2svg', params);\n      callProvided('pseudoElements2svg', params);\n      return callProvided('i2svg', params);\n    } else {\n      return Promise.reject(new Error('Operation requires a DOM of some kind.'));\n    }\n  },\n  watch: function () {\n    let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const {\n      autoReplaceSvgRoot\n    } = params;\n    if (config.autoReplaceSvg === false) {\n      config.autoReplaceSvg = true;\n    }\n    config.observeMutations = true;\n    domready(() => {\n      autoReplace({\n        autoReplaceSvgRoot\n      });\n      callHooks('watch', params);\n    });\n  }\n};\nconst parse = {\n  icon: icon => {\n    if (icon === null) {\n      return null;\n    }\n    if (typeof icon === 'object' && icon.prefix && icon.iconName) {\n      return {\n        prefix: icon.prefix,\n        iconName: byAlias(icon.prefix, icon.iconName) || icon.iconName\n      };\n    }\n    if (Array.isArray(icon) && icon.length === 2) {\n      const iconName = icon[1].indexOf('fa-') === 0 ? icon[1].slice(3) : icon[1];\n      const prefix = getCanonicalPrefix(icon[0]);\n      return {\n        prefix,\n        iconName: byAlias(prefix, iconName) || iconName\n      };\n    }\n    if (typeof icon === 'string' && (icon.indexOf(\"\".concat(config.cssPrefix, \"-\")) > -1 || icon.match(ICON_SELECTION_SYNTAX_PATTERN))) {\n      const canonicalIcon = getCanonicalIcon(icon.split(' '), {\n        skipLookups: true\n      });\n      return {\n        prefix: canonicalIcon.prefix || getDefaultUsablePrefix(),\n        iconName: byAlias(canonicalIcon.prefix, canonicalIcon.iconName) || canonicalIcon.iconName\n      };\n    }\n    if (typeof icon === 'string') {\n      const prefix = getDefaultUsablePrefix();\n      return {\n        prefix,\n        iconName: byAlias(prefix, icon) || icon\n      };\n    }\n  }\n};\nconst api = {\n  noAuto,\n  config,\n  dom,\n  parse,\n  library,\n  findIconDefinition,\n  toHtml\n};\nconst autoReplace = function () {\n  let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const {\n    autoReplaceSvgRoot = DOCUMENT\n  } = params;\n  if ((Object.keys(namespace.styles).length > 0 || config.autoFetchSvg) && IS_DOM && config.autoReplaceSvg) api.dom.i2svg({\n    node: autoReplaceSvgRoot\n  });\n};\n\nfunction domVariants(val, abstractCreator) {\n  Object.defineProperty(val, 'abstract', {\n    get: abstractCreator\n  });\n  Object.defineProperty(val, 'html', {\n    get: function () {\n      return val.abstract.map(a => toHtml(a));\n    }\n  });\n  Object.defineProperty(val, 'node', {\n    get: function () {\n      if (!IS_DOM) return;\n      const container = DOCUMENT.createElement('div');\n      container.innerHTML = val.html;\n      return container.children;\n    }\n  });\n  return val;\n}\n\nfunction asIcon (_ref) {\n  let {\n    children,\n    main,\n    mask,\n    attributes,\n    styles,\n    transform\n  } = _ref;\n  if (transformIsMeaningful(transform) && main.found && !mask.found) {\n    const {\n      width,\n      height\n    } = main;\n    const offset = {\n      x: width / height / 2,\n      y: 0.5\n    };\n    attributes['style'] = joinStyles(_objectSpread2(_objectSpread2({}, styles), {}, {\n      'transform-origin': \"\".concat(offset.x + transform.x / 16, \"em \").concat(offset.y + transform.y / 16, \"em\")\n    }));\n  }\n  return [{\n    tag: 'svg',\n    attributes,\n    children\n  }];\n}\n\nfunction asSymbol (_ref) {\n  let {\n    prefix,\n    iconName,\n    children,\n    attributes,\n    symbol\n  } = _ref;\n  const id = symbol === true ? \"\".concat(prefix, \"-\").concat(config.cssPrefix, \"-\").concat(iconName) : symbol;\n  return [{\n    tag: 'svg',\n    attributes: {\n      style: 'display: none;'\n    },\n    children: [{\n      tag: 'symbol',\n      attributes: _objectSpread2(_objectSpread2({}, attributes), {}, {\n        id\n      }),\n      children\n    }]\n  }];\n}\n\nfunction makeInlineSvgAbstract(params) {\n  const {\n    icons: {\n      main,\n      mask\n    },\n    prefix,\n    iconName,\n    transform,\n    symbol,\n    title,\n    maskId,\n    titleId,\n    extra,\n    watchable = false\n  } = params;\n  const {\n    width,\n    height\n  } = mask.found ? mask : main;\n  const isUploadedIcon = Lt.includes(prefix);\n  const attrClass = [config.replacementClass, iconName ? \"\".concat(config.cssPrefix, \"-\").concat(iconName) : ''].filter(c$$1 => extra.classes.indexOf(c$$1) === -1).filter(c$$1 => c$$1 !== '' || !!c$$1).concat(extra.classes).join(' ');\n  let content = {\n    children: [],\n    attributes: _objectSpread2(_objectSpread2({}, extra.attributes), {}, {\n      'data-prefix': prefix,\n      'data-icon': iconName,\n      'class': attrClass,\n      'role': extra.attributes.role || 'img',\n      'xmlns': 'http://www.w3.org/2000/svg',\n      'viewBox': \"0 0 \".concat(width, \" \").concat(height)\n    })\n  };\n  const uploadedIconWidthStyle = isUploadedIcon && !~extra.classes.indexOf('fa-fw') ? {\n    width: \"\".concat(width / height * 16 * 0.0625, \"em\")\n  } : {};\n  if (watchable) {\n    content.attributes[DATA_FA_I2SVG] = '';\n  }\n  if (title) {\n    content.children.push({\n      tag: 'title',\n      attributes: {\n        id: content.attributes['aria-labelledby'] || \"title-\".concat(titleId || nextUniqueId())\n      },\n      children: [title]\n    });\n    delete content.attributes.title;\n  }\n  const args = _objectSpread2(_objectSpread2({}, content), {}, {\n    prefix,\n    iconName,\n    main,\n    mask,\n    maskId,\n    transform,\n    symbol,\n    styles: _objectSpread2(_objectSpread2({}, uploadedIconWidthStyle), extra.styles)\n  });\n  const {\n    children,\n    attributes\n  } = mask.found && main.found ? callProvided('generateAbstractMask', args) || {\n    children: [],\n    attributes: {}\n  } : callProvided('generateAbstractIcon', args) || {\n    children: [],\n    attributes: {}\n  };\n  args.children = children;\n  args.attributes = attributes;\n  if (symbol) {\n    return asSymbol(args);\n  } else {\n    return asIcon(args);\n  }\n}\nfunction makeLayersTextAbstract(params) {\n  const {\n    content,\n    width,\n    height,\n    transform,\n    title,\n    extra,\n    watchable = false\n  } = params;\n  const attributes = _objectSpread2(_objectSpread2(_objectSpread2({}, extra.attributes), title ? {\n    'title': title\n  } : {}), {}, {\n    'class': extra.classes.join(' ')\n  });\n  if (watchable) {\n    attributes[DATA_FA_I2SVG] = '';\n  }\n  const styles = _objectSpread2({}, extra.styles);\n  if (transformIsMeaningful(transform)) {\n    styles['transform'] = transformForCss({\n      transform,\n      startCentered: true,\n      width,\n      height\n    });\n    styles['-webkit-transform'] = styles['transform'];\n  }\n  const styleString = joinStyles(styles);\n  if (styleString.length > 0) {\n    attributes['style'] = styleString;\n  }\n  const val = [];\n  val.push({\n    tag: 'span',\n    attributes,\n    children: [content]\n  });\n  if (title) {\n    val.push({\n      tag: 'span',\n      attributes: {\n        class: 'sr-only'\n      },\n      children: [title]\n    });\n  }\n  return val;\n}\nfunction makeLayersCounterAbstract(params) {\n  const {\n    content,\n    title,\n    extra\n  } = params;\n  const attributes = _objectSpread2(_objectSpread2(_objectSpread2({}, extra.attributes), title ? {\n    'title': title\n  } : {}), {}, {\n    'class': extra.classes.join(' ')\n  });\n  const styleString = joinStyles(extra.styles);\n  if (styleString.length > 0) {\n    attributes['style'] = styleString;\n  }\n  const val = [];\n  val.push({\n    tag: 'span',\n    attributes,\n    children: [content]\n  });\n  if (title) {\n    val.push({\n      tag: 'span',\n      attributes: {\n        class: 'sr-only'\n      },\n      children: [title]\n    });\n  }\n  return val;\n}\n\nconst {\n  styles: styles$1\n} = namespace;\nfunction asFoundIcon(icon) {\n  const width = icon[0];\n  const height = icon[1];\n  const [vectorData] = icon.slice(4);\n  let element = null;\n  if (Array.isArray(vectorData)) {\n    element = {\n      tag: 'g',\n      attributes: {\n        class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.GROUP)\n      },\n      children: [{\n        tag: 'path',\n        attributes: {\n          class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.SECONDARY),\n          fill: 'currentColor',\n          d: vectorData[0]\n        }\n      }, {\n        tag: 'path',\n        attributes: {\n          class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.PRIMARY),\n          fill: 'currentColor',\n          d: vectorData[1]\n        }\n      }]\n    };\n  } else {\n    element = {\n      tag: 'path',\n      attributes: {\n        fill: 'currentColor',\n        d: vectorData\n      }\n    };\n  }\n  return {\n    found: true,\n    width,\n    height,\n    icon: element\n  };\n}\nconst missingIconResolutionMixin = {\n  found: false,\n  width: 512,\n  height: 512\n};\nfunction maybeNotifyMissing(iconName, prefix) {\n  if (!PRODUCTION && !config.showMissingIcons && iconName) {\n    console.error(\"Icon with name \\\"\".concat(iconName, \"\\\" and prefix \\\"\").concat(prefix, \"\\\" is missing.\"));\n  }\n}\nfunction findIcon(iconName, prefix) {\n  let givenPrefix = prefix;\n  if (prefix === 'fa' && config.styleDefault !== null) {\n    prefix = getDefaultUsablePrefix();\n  }\n  return new Promise((resolve, reject) => {\n    if (givenPrefix === 'fa') {\n      const shim = byOldName(iconName) || {};\n      iconName = shim.iconName || iconName;\n      prefix = shim.prefix || prefix;\n    }\n    if (iconName && prefix && styles$1[prefix] && styles$1[prefix][iconName]) {\n      const icon = styles$1[prefix][iconName];\n      return resolve(asFoundIcon(icon));\n    }\n    maybeNotifyMissing(iconName, prefix);\n    resolve(_objectSpread2(_objectSpread2({}, missingIconResolutionMixin), {}, {\n      icon: config.showMissingIcons && iconName ? callProvided('missingIconAbstract') || {} : {}\n    }));\n  });\n}\n\nconst noop$1 = () => {};\nconst p$2 = config.measurePerformance && PERFORMANCE && PERFORMANCE.mark && PERFORMANCE.measure ? PERFORMANCE : {\n  mark: noop$1,\n  measure: noop$1\n};\nconst preamble = \"FA \\\"6.7.2\\\"\";\nconst begin = name => {\n  p$2.mark(\"\".concat(preamble, \" \").concat(name, \" begins\"));\n  return () => end(name);\n};\nconst end = name => {\n  p$2.mark(\"\".concat(preamble, \" \").concat(name, \" ends\"));\n  p$2.measure(\"\".concat(preamble, \" \").concat(name), \"\".concat(preamble, \" \").concat(name, \" begins\"), \"\".concat(preamble, \" \").concat(name, \" ends\"));\n};\nvar perf = {\n  begin,\n  end\n};\n\nconst noop$2 = () => {};\nfunction isWatched(node) {\n  const i2svg = node.getAttribute ? node.getAttribute(DATA_FA_I2SVG) : null;\n  return typeof i2svg === 'string';\n}\nfunction hasPrefixAndIcon(node) {\n  const prefix = node.getAttribute ? node.getAttribute(DATA_PREFIX) : null;\n  const icon = node.getAttribute ? node.getAttribute(DATA_ICON) : null;\n  return prefix && icon;\n}\nfunction hasBeenReplaced(node) {\n  return node && node.classList && node.classList.contains && node.classList.contains(config.replacementClass);\n}\nfunction getMutator() {\n  if (config.autoReplaceSvg === true) {\n    return mutators.replace;\n  }\n  const mutator = mutators[config.autoReplaceSvg];\n  return mutator || mutators.replace;\n}\nfunction createElementNS(tag) {\n  return DOCUMENT.createElementNS('http://www.w3.org/2000/svg', tag);\n}\nfunction createElement(tag) {\n  return DOCUMENT.createElement(tag);\n}\nfunction convertSVG(abstractObj) {\n  let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    ceFn = abstractObj.tag === 'svg' ? createElementNS : createElement\n  } = params;\n  if (typeof abstractObj === 'string') {\n    return DOCUMENT.createTextNode(abstractObj);\n  }\n  const tag = ceFn(abstractObj.tag);\n  Object.keys(abstractObj.attributes || []).forEach(function (key) {\n    tag.setAttribute(key, abstractObj.attributes[key]);\n  });\n  const children = abstractObj.children || [];\n  children.forEach(function (child) {\n    tag.appendChild(convertSVG(child, {\n      ceFn\n    }));\n  });\n  return tag;\n}\nfunction nodeAsComment(node) {\n  let comment = \" \".concat(node.outerHTML, \" \");\n  /* BEGIN.ATTRIBUTION */\n  comment = \"\".concat(comment, \"Font Awesome fontawesome.com \");\n  /* END.ATTRIBUTION */\n  return comment;\n}\nconst mutators = {\n  replace: function (mutation) {\n    const node = mutation[0];\n    if (node.parentNode) {\n      mutation[1].forEach(abstract => {\n        node.parentNode.insertBefore(convertSVG(abstract), node);\n      });\n      if (node.getAttribute(DATA_FA_I2SVG) === null && config.keepOriginalSource) {\n        let comment = DOCUMENT.createComment(nodeAsComment(node));\n        node.parentNode.replaceChild(comment, node);\n      } else {\n        node.remove();\n      }\n    }\n  },\n  nest: function (mutation) {\n    const node = mutation[0];\n    const abstract = mutation[1];\n\n    // If we already have a replaced node we do not want to continue nesting within it.\n    // Short-circuit to the standard replacement\n    if (~classArray(node).indexOf(config.replacementClass)) {\n      return mutators.replace(mutation);\n    }\n    const forSvg = new RegExp(\"\".concat(config.cssPrefix, \"-.*\"));\n    delete abstract[0].attributes.id;\n    if (abstract[0].attributes.class) {\n      const splitClasses = abstract[0].attributes.class.split(' ').reduce((acc, cls) => {\n        if (cls === config.replacementClass || cls.match(forSvg)) {\n          acc.toSvg.push(cls);\n        } else {\n          acc.toNode.push(cls);\n        }\n        return acc;\n      }, {\n        toNode: [],\n        toSvg: []\n      });\n      abstract[0].attributes.class = splitClasses.toSvg.join(' ');\n      if (splitClasses.toNode.length === 0) {\n        node.removeAttribute('class');\n      } else {\n        node.setAttribute('class', splitClasses.toNode.join(' '));\n      }\n    }\n    const newInnerHTML = abstract.map(a => toHtml(a)).join('\\n');\n    node.setAttribute(DATA_FA_I2SVG, '');\n    node.innerHTML = newInnerHTML;\n  }\n};\nfunction performOperationSync(op) {\n  op();\n}\nfunction perform(mutations, callback) {\n  const callbackFunction = typeof callback === 'function' ? callback : noop$2;\n  if (mutations.length === 0) {\n    callbackFunction();\n  } else {\n    let frame = performOperationSync;\n    if (config.mutateApproach === MUTATION_APPROACH_ASYNC) {\n      frame = WINDOW.requestAnimationFrame || performOperationSync;\n    }\n    frame(() => {\n      const mutator = getMutator();\n      const mark = perf.begin('mutate');\n      mutations.map(mutator);\n      mark();\n      callbackFunction();\n    });\n  }\n}\nlet disabled = false;\nfunction disableObservation() {\n  disabled = true;\n}\nfunction enableObservation() {\n  disabled = false;\n}\nlet mo = null;\nfunction observe(options) {\n  if (!MUTATION_OBSERVER) {\n    return;\n  }\n  if (!config.observeMutations) {\n    return;\n  }\n  const {\n    treeCallback = noop$2,\n    nodeCallback = noop$2,\n    pseudoElementsCallback = noop$2,\n    observeMutationsRoot = DOCUMENT\n  } = options;\n  mo = new MUTATION_OBSERVER(objects => {\n    if (disabled) return;\n    const defaultPrefix = getDefaultUsablePrefix();\n    toArray(objects).forEach(mutationRecord => {\n      if (mutationRecord.type === 'childList' && mutationRecord.addedNodes.length > 0 && !isWatched(mutationRecord.addedNodes[0])) {\n        if (config.searchPseudoElements) {\n          pseudoElementsCallback(mutationRecord.target);\n        }\n        treeCallback(mutationRecord.target);\n      }\n      if (mutationRecord.type === 'attributes' && mutationRecord.target.parentNode && config.searchPseudoElements) {\n        pseudoElementsCallback(mutationRecord.target.parentNode);\n      }\n      if (mutationRecord.type === 'attributes' && isWatched(mutationRecord.target) && ~ATTRIBUTES_WATCHED_FOR_MUTATION.indexOf(mutationRecord.attributeName)) {\n        if (mutationRecord.attributeName === 'class' && hasPrefixAndIcon(mutationRecord.target)) {\n          const {\n            prefix,\n            iconName\n          } = getCanonicalIcon(classArray(mutationRecord.target));\n          mutationRecord.target.setAttribute(DATA_PREFIX, prefix || defaultPrefix);\n          if (iconName) mutationRecord.target.setAttribute(DATA_ICON, iconName);\n        } else if (hasBeenReplaced(mutationRecord.target)) {\n          nodeCallback(mutationRecord.target);\n        }\n      }\n    });\n  });\n  if (!IS_DOM) return;\n  mo.observe(observeMutationsRoot, {\n    childList: true,\n    attributes: true,\n    characterData: true,\n    subtree: true\n  });\n}\nfunction disconnect() {\n  if (!mo) return;\n  mo.disconnect();\n}\n\nfunction styleParser (node) {\n  const style = node.getAttribute('style');\n  let val = [];\n  if (style) {\n    val = style.split(';').reduce((acc, style) => {\n      const styles = style.split(':');\n      const prop = styles[0];\n      const value = styles.slice(1);\n      if (prop && value.length > 0) {\n        acc[prop] = value.join(':').trim();\n      }\n      return acc;\n    }, {});\n  }\n  return val;\n}\n\nfunction classParser (node) {\n  const existingPrefix = node.getAttribute('data-prefix');\n  const existingIconName = node.getAttribute('data-icon');\n  const innerText = node.innerText !== undefined ? node.innerText.trim() : '';\n  let val = getCanonicalIcon(classArray(node));\n  if (!val.prefix) {\n    val.prefix = getDefaultUsablePrefix();\n  }\n  if (existingPrefix && existingIconName) {\n    val.prefix = existingPrefix;\n    val.iconName = existingIconName;\n  }\n  if (val.iconName && val.prefix) {\n    return val;\n  }\n  if (val.prefix && innerText.length > 0) {\n    val.iconName = byLigature(val.prefix, node.innerText) || byUnicode(val.prefix, toHex(node.innerText));\n  }\n  if (!val.iconName && config.autoFetchSvg && node.firstChild && node.firstChild.nodeType === Node.TEXT_NODE) {\n    val.iconName = node.firstChild.data;\n  }\n  return val;\n}\n\nfunction attributesParser (node) {\n  const extraAttributes = toArray(node.attributes).reduce((acc, attr) => {\n    if (acc.name !== 'class' && acc.name !== 'style') {\n      acc[attr.name] = attr.value;\n    }\n    return acc;\n  }, {});\n  const title = node.getAttribute('title');\n  const titleId = node.getAttribute('data-fa-title-id');\n  if (config.autoA11y) {\n    if (title) {\n      extraAttributes['aria-labelledby'] = \"\".concat(config.replacementClass, \"-title-\").concat(titleId || nextUniqueId());\n    } else {\n      extraAttributes['aria-hidden'] = 'true';\n      extraAttributes['focusable'] = 'false';\n    }\n  }\n  return extraAttributes;\n}\n\nfunction blankMeta() {\n  return {\n    iconName: null,\n    title: null,\n    titleId: null,\n    prefix: null,\n    transform: meaninglessTransform,\n    symbol: false,\n    mask: {\n      iconName: null,\n      prefix: null,\n      rest: []\n    },\n    maskId: null,\n    extra: {\n      classes: [],\n      styles: {},\n      attributes: {}\n    }\n  };\n}\nfunction parseMeta(node) {\n  let parser = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    styleParser: true\n  };\n  const {\n    iconName,\n    prefix,\n    rest: extraClasses\n  } = classParser(node);\n  const extraAttributes = attributesParser(node);\n  const pluginMeta = chainHooks('parseNodeAttributes', {}, node);\n  let extraStyles = parser.styleParser ? styleParser(node) : [];\n  return _objectSpread2({\n    iconName,\n    title: node.getAttribute('title'),\n    titleId: node.getAttribute('data-fa-title-id'),\n    prefix,\n    transform: meaninglessTransform,\n    mask: {\n      iconName: null,\n      prefix: null,\n      rest: []\n    },\n    maskId: null,\n    symbol: false,\n    extra: {\n      classes: extraClasses,\n      styles: extraStyles,\n      attributes: extraAttributes\n    }\n  }, pluginMeta);\n}\n\nconst {\n  styles: styles$2\n} = namespace;\nfunction generateMutation(node) {\n  const nodeMeta = config.autoReplaceSvg === 'nest' ? parseMeta(node, {\n    styleParser: false\n  }) : parseMeta(node);\n  if (~nodeMeta.extra.classes.indexOf(LAYERS_TEXT_CLASSNAME)) {\n    return callProvided('generateLayersText', node, nodeMeta);\n  } else {\n    return callProvided('generateSvgReplacementMutation', node, nodeMeta);\n  }\n}\nfunction getKnownPrefixes() {\n  return [...Ft, ...Ia];\n}\nfunction onTree(root) {\n  let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  if (!IS_DOM) return Promise.resolve();\n  const htmlClassList = DOCUMENT.documentElement.classList;\n  const hclAdd = suffix => htmlClassList.add(\"\".concat(HTML_CLASS_I2SVG_BASE_CLASS, \"-\").concat(suffix));\n  const hclRemove = suffix => htmlClassList.remove(\"\".concat(HTML_CLASS_I2SVG_BASE_CLASS, \"-\").concat(suffix));\n  const prefixes = config.autoFetchSvg ? getKnownPrefixes() : P.concat(Object.keys(styles$2));\n  if (!prefixes.includes('fa')) {\n    prefixes.push('fa');\n  }\n  const prefixesDomQuery = [\".\".concat(LAYERS_TEXT_CLASSNAME, \":not([\").concat(DATA_FA_I2SVG, \"])\")].concat(prefixes.map(p$$1 => \".\".concat(p$$1, \":not([\").concat(DATA_FA_I2SVG, \"])\"))).join(', ');\n  if (prefixesDomQuery.length === 0) {\n    return Promise.resolve();\n  }\n  let candidates = [];\n  try {\n    candidates = toArray(root.querySelectorAll(prefixesDomQuery));\n  } catch (e$$1) {\n    // noop\n  }\n  if (candidates.length > 0) {\n    hclAdd('pending');\n    hclRemove('complete');\n  } else {\n    return Promise.resolve();\n  }\n  const mark = perf.begin('onTree');\n  const mutations = candidates.reduce((acc, node) => {\n    try {\n      const mutation = generateMutation(node);\n      if (mutation) {\n        acc.push(mutation);\n      }\n    } catch (e$$1) {\n      if (!PRODUCTION) {\n        if (e$$1.name === 'MissingIcon') {\n          console.error(e$$1);\n        }\n      }\n    }\n    return acc;\n  }, []);\n  return new Promise((resolve, reject) => {\n    Promise.all(mutations).then(resolvedMutations => {\n      perform(resolvedMutations, () => {\n        hclAdd('active');\n        hclAdd('complete');\n        hclRemove('pending');\n        if (typeof callback === 'function') callback();\n        mark();\n        resolve();\n      });\n    }).catch(e$$1 => {\n      mark();\n      reject(e$$1);\n    });\n  });\n}\nfunction onNode(node) {\n  let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  generateMutation(node).then(mutation => {\n    if (mutation) {\n      perform([mutation], callback);\n    }\n  });\n}\nfunction resolveIcons(next) {\n  return function (maybeIconDefinition) {\n    let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const iconDefinition = (maybeIconDefinition || {}).icon ? maybeIconDefinition : findIconDefinition(maybeIconDefinition || {});\n    let {\n      mask\n    } = params;\n    if (mask) {\n      mask = (mask || {}).icon ? mask : findIconDefinition(mask || {});\n    }\n    return next(iconDefinition, _objectSpread2(_objectSpread2({}, params), {}, {\n      mask\n    }));\n  };\n}\nconst render = function (iconDefinition) {\n  let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    transform = meaninglessTransform,\n    symbol = false,\n    mask = null,\n    maskId = null,\n    title = null,\n    titleId = null,\n    classes = [],\n    attributes = {},\n    styles = {}\n  } = params;\n  if (!iconDefinition) return;\n  const {\n    prefix,\n    iconName,\n    icon\n  } = iconDefinition;\n  return domVariants(_objectSpread2({\n    type: 'icon'\n  }, iconDefinition), () => {\n    callHooks('beforeDOMElementCreation', {\n      iconDefinition,\n      params\n    });\n    if (config.autoA11y) {\n      if (title) {\n        attributes['aria-labelledby'] = \"\".concat(config.replacementClass, \"-title-\").concat(titleId || nextUniqueId());\n      } else {\n        attributes['aria-hidden'] = 'true';\n        attributes['focusable'] = 'false';\n      }\n    }\n    return makeInlineSvgAbstract({\n      icons: {\n        main: asFoundIcon(icon),\n        mask: mask ? asFoundIcon(mask.icon) : {\n          found: false,\n          width: null,\n          height: null,\n          icon: {}\n        }\n      },\n      prefix,\n      iconName,\n      transform: _objectSpread2(_objectSpread2({}, meaninglessTransform), transform),\n      symbol,\n      title,\n      maskId,\n      titleId,\n      extra: {\n        attributes,\n        styles,\n        classes\n      }\n    });\n  });\n};\nvar ReplaceElements = {\n  mixout() {\n    return {\n      icon: resolveIcons(render)\n    };\n  },\n  hooks() {\n    return {\n      mutationObserverCallbacks(accumulator) {\n        accumulator.treeCallback = onTree;\n        accumulator.nodeCallback = onNode;\n        return accumulator;\n      }\n    };\n  },\n  provides(providers$$1) {\n    providers$$1.i2svg = function (params) {\n      const {\n        node = DOCUMENT,\n        callback = () => {}\n      } = params;\n      return onTree(node, callback);\n    };\n    providers$$1.generateSvgReplacementMutation = function (node, nodeMeta) {\n      const {\n        iconName,\n        title,\n        titleId,\n        prefix,\n        transform,\n        symbol,\n        mask,\n        maskId,\n        extra\n      } = nodeMeta;\n      return new Promise((resolve, reject) => {\n        Promise.all([findIcon(iconName, prefix), mask.iconName ? findIcon(mask.iconName, mask.prefix) : Promise.resolve({\n          found: false,\n          width: 512,\n          height: 512,\n          icon: {}\n        })]).then(_ref => {\n          let [main, mask] = _ref;\n          resolve([node, makeInlineSvgAbstract({\n            icons: {\n              main,\n              mask\n            },\n            prefix,\n            iconName,\n            transform,\n            symbol,\n            maskId,\n            title,\n            titleId,\n            extra,\n            watchable: true\n          })]);\n        }).catch(reject);\n      });\n    };\n    providers$$1.generateAbstractIcon = function (_ref2) {\n      let {\n        children,\n        attributes,\n        main,\n        transform,\n        styles\n      } = _ref2;\n      const styleString = joinStyles(styles);\n      if (styleString.length > 0) {\n        attributes['style'] = styleString;\n      }\n      let nextChild;\n      if (transformIsMeaningful(transform)) {\n        nextChild = callProvided('generateAbstractTransformGrouping', {\n          main,\n          transform,\n          containerWidth: main.width,\n          iconWidth: main.width\n        });\n      }\n      children.push(nextChild || main.icon);\n      return {\n        children,\n        attributes\n      };\n    };\n  }\n};\n\nvar Layers = {\n  mixout() {\n    return {\n      layer(assembler) {\n        let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        const {\n          classes = []\n        } = params;\n        return domVariants({\n          type: 'layer'\n        }, () => {\n          callHooks('beforeDOMElementCreation', {\n            assembler,\n            params\n          });\n          let children = [];\n          assembler(args => {\n            Array.isArray(args) ? args.map(a => {\n              children = children.concat(a.abstract);\n            }) : children = children.concat(args.abstract);\n          });\n          return [{\n            tag: 'span',\n            attributes: {\n              class: [\"\".concat(config.cssPrefix, \"-layers\"), ...classes].join(' ')\n            },\n            children\n          }];\n        });\n      }\n    };\n  }\n};\n\nvar LayersCounter = {\n  mixout() {\n    return {\n      counter(content) {\n        let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        const {\n          title = null,\n          classes = [],\n          attributes = {},\n          styles = {}\n        } = params;\n        return domVariants({\n          type: 'counter',\n          content\n        }, () => {\n          callHooks('beforeDOMElementCreation', {\n            content,\n            params\n          });\n          return makeLayersCounterAbstract({\n            content: content.toString(),\n            title,\n            extra: {\n              attributes,\n              styles,\n              classes: [\"\".concat(config.cssPrefix, \"-layers-counter\"), ...classes]\n            }\n          });\n        });\n      }\n    };\n  }\n};\n\nvar LayersText = {\n  mixout() {\n    return {\n      text(content) {\n        let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        const {\n          transform = meaninglessTransform,\n          title = null,\n          classes = [],\n          attributes = {},\n          styles = {}\n        } = params;\n        return domVariants({\n          type: 'text',\n          content\n        }, () => {\n          callHooks('beforeDOMElementCreation', {\n            content,\n            params\n          });\n          return makeLayersTextAbstract({\n            content,\n            transform: _objectSpread2(_objectSpread2({}, meaninglessTransform), transform),\n            title,\n            extra: {\n              attributes,\n              styles,\n              classes: [\"\".concat(config.cssPrefix, \"-layers-text\"), ...classes]\n            }\n          });\n        });\n      }\n    };\n  },\n  provides(providers$$1) {\n    providers$$1.generateLayersText = function (node, nodeMeta) {\n      const {\n        title,\n        transform,\n        extra\n      } = nodeMeta;\n      let width = null;\n      let height = null;\n      if (IS_IE) {\n        const computedFontSize = parseInt(getComputedStyle(node).fontSize, 10);\n        const boundingClientRect = node.getBoundingClientRect();\n        width = boundingClientRect.width / computedFontSize;\n        height = boundingClientRect.height / computedFontSize;\n      }\n      if (config.autoA11y && !title) {\n        extra.attributes['aria-hidden'] = 'true';\n      }\n      return Promise.resolve([node, makeLayersTextAbstract({\n        content: node.innerHTML,\n        width,\n        height,\n        transform,\n        title,\n        extra,\n        watchable: true\n      })]);\n    };\n  }\n};\n\nconst CLEAN_CONTENT_PATTERN = new RegExp('\\u{22}', 'ug');\nconst SECONDARY_UNICODE_RANGE = [1105920, 1112319];\nconst _FONT_FAMILY_WEIGHT_TO_PREFIX = _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, {\n  FontAwesome: {\n    normal: 'fas',\n    400: 'fas'\n  }\n}), lt), wa), Yt);\nconst FONT_FAMILY_WEIGHT_TO_PREFIX = Object.keys(_FONT_FAMILY_WEIGHT_TO_PREFIX).reduce((acc, key) => {\n  acc[key.toLowerCase()] = _FONT_FAMILY_WEIGHT_TO_PREFIX[key];\n  return acc;\n}, {});\nconst FONT_FAMILY_WEIGHT_FALLBACK = Object.keys(FONT_FAMILY_WEIGHT_TO_PREFIX).reduce((acc, fontFamily) => {\n  const weights = FONT_FAMILY_WEIGHT_TO_PREFIX[fontFamily];\n  acc[fontFamily] = weights[900] || [...Object.entries(weights)][0][1];\n  return acc;\n}, {});\nfunction hexValueFromContent(content) {\n  const cleaned = content.replace(CLEAN_CONTENT_PATTERN, '');\n  const codePoint = codePointAt(cleaned, 0);\n  const isPrependTen = codePoint >= SECONDARY_UNICODE_RANGE[0] && codePoint <= SECONDARY_UNICODE_RANGE[1];\n  const isDoubled = cleaned.length === 2 ? cleaned[0] === cleaned[1] : false;\n  return {\n    value: isDoubled ? toHex(cleaned[0]) : toHex(cleaned),\n    isSecondary: isPrependTen || isDoubled\n  };\n}\nfunction getPrefix(fontFamily, fontWeight) {\n  const fontFamilySanitized = fontFamily.replace(/^['\"]|['\"]$/g, '').toLowerCase();\n  const fontWeightInteger = parseInt(fontWeight);\n  const fontWeightSanitized = isNaN(fontWeightInteger) ? 'normal' : fontWeightInteger;\n  return (FONT_FAMILY_WEIGHT_TO_PREFIX[fontFamilySanitized] || {})[fontWeightSanitized] || FONT_FAMILY_WEIGHT_FALLBACK[fontFamilySanitized];\n}\nfunction replaceForPosition(node, position) {\n  const pendingAttribute = \"\".concat(DATA_FA_PSEUDO_ELEMENT_PENDING).concat(position.replace(':', '-'));\n  return new Promise((resolve, reject) => {\n    if (node.getAttribute(pendingAttribute) !== null) {\n      // This node is already being processed\n      return resolve();\n    }\n    const children = toArray(node.children);\n    const alreadyProcessedPseudoElement = children.filter(c$$1 => c$$1.getAttribute(DATA_FA_PSEUDO_ELEMENT) === position)[0];\n    const styles = WINDOW.getComputedStyle(node, position);\n    const fontFamily = styles.getPropertyValue('font-family');\n    const fontFamilyMatch = fontFamily.match(FONT_FAMILY_PATTERN);\n    const fontWeight = styles.getPropertyValue('font-weight');\n    const content = styles.getPropertyValue('content');\n    if (alreadyProcessedPseudoElement && !fontFamilyMatch) {\n      // If we've already processed it but the current computed style does not result in a font-family,\n      // that probably means that a class name that was previously present to make the icon has been\n      // removed. So we now should delete the icon.\n      node.removeChild(alreadyProcessedPseudoElement);\n      return resolve();\n    } else if (fontFamilyMatch && content !== 'none' && content !== '') {\n      const content = styles.getPropertyValue('content');\n      let prefix = getPrefix(fontFamily, fontWeight);\n      const {\n        value: hexValue,\n        isSecondary\n      } = hexValueFromContent(content);\n      const isV4 = fontFamilyMatch[0].startsWith('FontAwesome');\n      let iconName = byUnicode(prefix, hexValue);\n      let iconIdentifier = iconName;\n      if (isV4) {\n        const iconName4 = byOldUnicode(hexValue);\n        if (iconName4.iconName && iconName4.prefix) {\n          iconName = iconName4.iconName;\n          prefix = iconName4.prefix;\n        }\n      }\n\n      // Only convert the pseudo element in this ::before/::after position into an icon if we haven't\n      // already done so with the same prefix and iconName\n      if (iconName && !isSecondary && (!alreadyProcessedPseudoElement || alreadyProcessedPseudoElement.getAttribute(DATA_PREFIX) !== prefix || alreadyProcessedPseudoElement.getAttribute(DATA_ICON) !== iconIdentifier)) {\n        node.setAttribute(pendingAttribute, iconIdentifier);\n        if (alreadyProcessedPseudoElement) {\n          // Delete the old one, since we're replacing it with a new one\n          node.removeChild(alreadyProcessedPseudoElement);\n        }\n        const meta = blankMeta();\n        const {\n          extra\n        } = meta;\n        extra.attributes[DATA_FA_PSEUDO_ELEMENT] = position;\n        findIcon(iconName, prefix).then(main => {\n          const abstract = makeInlineSvgAbstract(_objectSpread2(_objectSpread2({}, meta), {}, {\n            icons: {\n              main,\n              mask: emptyCanonicalIcon()\n            },\n            prefix,\n            iconName: iconIdentifier,\n            extra,\n            watchable: true\n          }));\n          const element = DOCUMENT.createElementNS('http://www.w3.org/2000/svg', 'svg');\n          if (position === '::before') {\n            node.insertBefore(element, node.firstChild);\n          } else {\n            node.appendChild(element);\n          }\n          element.outerHTML = abstract.map(a$$1 => toHtml(a$$1)).join('\\n');\n          node.removeAttribute(pendingAttribute);\n          resolve();\n        }).catch(reject);\n      } else {\n        resolve();\n      }\n    } else {\n      resolve();\n    }\n  });\n}\nfunction replace(node) {\n  return Promise.all([replaceForPosition(node, '::before'), replaceForPosition(node, '::after')]);\n}\nfunction processable(node) {\n  return node.parentNode !== document.head && !~TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS.indexOf(node.tagName.toUpperCase()) && !node.getAttribute(DATA_FA_PSEUDO_ELEMENT) && (!node.parentNode || node.parentNode.tagName !== 'svg');\n}\nfunction searchPseudoElements(root) {\n  if (!IS_DOM) return;\n  return new Promise((resolve, reject) => {\n    const operations = toArray(root.querySelectorAll('*')).filter(processable).map(replace);\n    const end = perf.begin('searchPseudoElements');\n    disableObservation();\n    Promise.all(operations).then(() => {\n      end();\n      enableObservation();\n      resolve();\n    }).catch(() => {\n      end();\n      enableObservation();\n      reject();\n    });\n  });\n}\nvar PseudoElements = {\n  hooks() {\n    return {\n      mutationObserverCallbacks(accumulator) {\n        accumulator.pseudoElementsCallback = searchPseudoElements;\n        return accumulator;\n      }\n    };\n  },\n  provides(providers) {\n    providers.pseudoElements2svg = function (params) {\n      const {\n        node = DOCUMENT\n      } = params;\n      if (config.searchPseudoElements) {\n        searchPseudoElements(node);\n      }\n    };\n  }\n};\n\nlet _unwatched = false;\nvar MutationObserver$1 = {\n  mixout() {\n    return {\n      dom: {\n        unwatch() {\n          disableObservation();\n          _unwatched = true;\n        }\n      }\n    };\n  },\n  hooks() {\n    return {\n      bootstrap() {\n        observe(chainHooks('mutationObserverCallbacks', {}));\n      },\n      noAuto() {\n        disconnect();\n      },\n      watch(params) {\n        const {\n          observeMutationsRoot\n        } = params;\n        if (_unwatched) {\n          enableObservation();\n        } else {\n          observe(chainHooks('mutationObserverCallbacks', {\n            observeMutationsRoot\n          }));\n        }\n      }\n    };\n  }\n};\n\nconst parseTransformString = transformString => {\n  let transform = {\n    size: 16,\n    x: 0,\n    y: 0,\n    flipX: false,\n    flipY: false,\n    rotate: 0\n  };\n  return transformString.toLowerCase().split(' ').reduce((acc, n) => {\n    const parts = n.toLowerCase().split('-');\n    const first = parts[0];\n    let rest = parts.slice(1).join('-');\n    if (first && rest === 'h') {\n      acc.flipX = true;\n      return acc;\n    }\n    if (first && rest === 'v') {\n      acc.flipY = true;\n      return acc;\n    }\n    rest = parseFloat(rest);\n    if (isNaN(rest)) {\n      return acc;\n    }\n    switch (first) {\n      case 'grow':\n        acc.size = acc.size + rest;\n        break;\n      case 'shrink':\n        acc.size = acc.size - rest;\n        break;\n      case 'left':\n        acc.x = acc.x - rest;\n        break;\n      case 'right':\n        acc.x = acc.x + rest;\n        break;\n      case 'up':\n        acc.y = acc.y - rest;\n        break;\n      case 'down':\n        acc.y = acc.y + rest;\n        break;\n      case 'rotate':\n        acc.rotate = acc.rotate + rest;\n        break;\n    }\n    return acc;\n  }, transform);\n};\nvar PowerTransforms = {\n  mixout() {\n    return {\n      parse: {\n        transform: transformString => {\n          return parseTransformString(transformString);\n        }\n      }\n    };\n  },\n  hooks() {\n    return {\n      parseNodeAttributes(accumulator, node) {\n        const transformString = node.getAttribute('data-fa-transform');\n        if (transformString) {\n          accumulator.transform = parseTransformString(transformString);\n        }\n        return accumulator;\n      }\n    };\n  },\n  provides(providers) {\n    providers.generateAbstractTransformGrouping = function (_ref) {\n      let {\n        main,\n        transform,\n        containerWidth,\n        iconWidth\n      } = _ref;\n      const outer = {\n        transform: \"translate(\".concat(containerWidth / 2, \" 256)\")\n      };\n      const innerTranslate = \"translate(\".concat(transform.x * 32, \", \").concat(transform.y * 32, \") \");\n      const innerScale = \"scale(\".concat(transform.size / 16 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / 16 * (transform.flipY ? -1 : 1), \") \");\n      const innerRotate = \"rotate(\".concat(transform.rotate, \" 0 0)\");\n      const inner = {\n        transform: \"\".concat(innerTranslate, \" \").concat(innerScale, \" \").concat(innerRotate)\n      };\n      const path = {\n        transform: \"translate(\".concat(iconWidth / 2 * -1, \" -256)\")\n      };\n      const operations = {\n        outer,\n        inner,\n        path\n      };\n      return {\n        tag: 'g',\n        attributes: _objectSpread2({}, operations.outer),\n        children: [{\n          tag: 'g',\n          attributes: _objectSpread2({}, operations.inner),\n          children: [{\n            tag: main.icon.tag,\n            children: main.icon.children,\n            attributes: _objectSpread2(_objectSpread2({}, main.icon.attributes), operations.path)\n          }]\n        }]\n      };\n    };\n  }\n};\n\nconst ALL_SPACE = {\n  x: 0,\n  y: 0,\n  width: '100%',\n  height: '100%'\n};\nfunction fillBlack(abstract) {\n  let force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  if (abstract.attributes && (abstract.attributes.fill || force)) {\n    abstract.attributes.fill = 'black';\n  }\n  return abstract;\n}\nfunction deGroup(abstract) {\n  if (abstract.tag === 'g') {\n    return abstract.children;\n  } else {\n    return [abstract];\n  }\n}\nvar Masks = {\n  hooks() {\n    return {\n      parseNodeAttributes(accumulator, node) {\n        const maskData = node.getAttribute('data-fa-mask');\n        const mask = !maskData ? emptyCanonicalIcon() : getCanonicalIcon(maskData.split(' ').map(i => i.trim()));\n        if (!mask.prefix) {\n          mask.prefix = getDefaultUsablePrefix();\n        }\n        accumulator.mask = mask;\n        accumulator.maskId = node.getAttribute('data-fa-mask-id');\n        return accumulator;\n      }\n    };\n  },\n  provides(providers) {\n    providers.generateAbstractMask = function (_ref) {\n      let {\n        children,\n        attributes,\n        main,\n        mask,\n        maskId: explicitMaskId,\n        transform\n      } = _ref;\n      const {\n        width: mainWidth,\n        icon: mainPath\n      } = main;\n      const {\n        width: maskWidth,\n        icon: maskPath\n      } = mask;\n      const trans = transformForSvg({\n        transform,\n        containerWidth: maskWidth,\n        iconWidth: mainWidth\n      });\n      const maskRect = {\n        tag: 'rect',\n        attributes: _objectSpread2(_objectSpread2({}, ALL_SPACE), {}, {\n          fill: 'white'\n        })\n      };\n      const maskInnerGroupChildrenMixin = mainPath.children ? {\n        children: mainPath.children.map(fillBlack)\n      } : {};\n      const maskInnerGroup = {\n        tag: 'g',\n        attributes: _objectSpread2({}, trans.inner),\n        children: [fillBlack(_objectSpread2({\n          tag: mainPath.tag,\n          attributes: _objectSpread2(_objectSpread2({}, mainPath.attributes), trans.path)\n        }, maskInnerGroupChildrenMixin))]\n      };\n      const maskOuterGroup = {\n        tag: 'g',\n        attributes: _objectSpread2({}, trans.outer),\n        children: [maskInnerGroup]\n      };\n      const maskId = \"mask-\".concat(explicitMaskId || nextUniqueId());\n      const clipId = \"clip-\".concat(explicitMaskId || nextUniqueId());\n      const maskTag = {\n        tag: 'mask',\n        attributes: _objectSpread2(_objectSpread2({}, ALL_SPACE), {}, {\n          id: maskId,\n          maskUnits: 'userSpaceOnUse',\n          maskContentUnits: 'userSpaceOnUse'\n        }),\n        children: [maskRect, maskOuterGroup]\n      };\n      const defs = {\n        tag: 'defs',\n        children: [{\n          tag: 'clipPath',\n          attributes: {\n            id: clipId\n          },\n          children: deGroup(maskPath)\n        }, maskTag]\n      };\n      children.push(defs, {\n        tag: 'rect',\n        attributes: _objectSpread2({\n          fill: 'currentColor',\n          'clip-path': \"url(#\".concat(clipId, \")\"),\n          mask: \"url(#\".concat(maskId, \")\")\n        }, ALL_SPACE)\n      });\n      return {\n        children,\n        attributes\n      };\n    };\n  }\n};\n\nvar MissingIconIndicator = {\n  provides(providers) {\n    let reduceMotion = false;\n    if (WINDOW.matchMedia) {\n      reduceMotion = WINDOW.matchMedia('(prefers-reduced-motion: reduce)').matches;\n    }\n    providers.missingIconAbstract = function () {\n      const gChildren = [];\n      const FILL = {\n        fill: 'currentColor'\n      };\n      const ANIMATION_BASE = {\n        attributeType: 'XML',\n        repeatCount: 'indefinite',\n        dur: '2s'\n      };\n\n      // Ring\n      gChildren.push({\n        tag: 'path',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          d: 'M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z'\n        })\n      });\n      const OPACITY_ANIMATE = _objectSpread2(_objectSpread2({}, ANIMATION_BASE), {}, {\n        attributeName: 'opacity'\n      });\n      const dot = {\n        tag: 'circle',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          cx: '256',\n          cy: '364',\n          r: '28'\n        }),\n        children: []\n      };\n      if (!reduceMotion) {\n        dot.children.push({\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, ANIMATION_BASE), {}, {\n            attributeName: 'r',\n            values: '28;14;28;28;14;28;'\n          })\n        }, {\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n            values: '1;0;1;1;0;1;'\n          })\n        });\n      }\n      gChildren.push(dot);\n      gChildren.push({\n        tag: 'path',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          opacity: '1',\n          d: 'M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z'\n        }),\n        children: reduceMotion ? [] : [{\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n            values: '1;0;0;0;0;1;'\n          })\n        }]\n      });\n      if (!reduceMotion) {\n        // Exclamation\n        gChildren.push({\n          tag: 'path',\n          attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n            opacity: '0',\n            d: 'M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z'\n          }),\n          children: [{\n            tag: 'animate',\n            attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n              values: '0;0;1;1;0;0;'\n            })\n          }]\n        });\n      }\n      return {\n        tag: 'g',\n        attributes: {\n          'class': 'missing'\n        },\n        children: gChildren\n      };\n    };\n  }\n};\n\nvar SvgSymbols = {\n  hooks() {\n    return {\n      parseNodeAttributes(accumulator, node) {\n        const symbolData = node.getAttribute('data-fa-symbol');\n        const symbol = symbolData === null ? false : symbolData === '' ? true : symbolData;\n        accumulator['symbol'] = symbol;\n        return accumulator;\n      }\n    };\n  }\n};\n\nvar plugins = [InjectCSS, ReplaceElements, Layers, LayersCounter, LayersText, PseudoElements, MutationObserver$1, PowerTransforms, Masks, MissingIconIndicator, SvgSymbols];\n\nregisterPlugins(plugins, {\n  mixoutsTo: api\n});\nconst noAuto$1 = api.noAuto;\nconst config$1 = api.config;\nconst library$1 = api.library;\nconst dom$1 = api.dom;\nconst parse$1 = api.parse;\nconst findIconDefinition$1 = api.findIconDefinition;\nconst toHtml$1 = api.toHtml;\nconst icon = api.icon;\nconst layer = api.layer;\nconst text = api.text;\nconst counter = api.counter;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/index.mjs\n");

/***/ })

};
;