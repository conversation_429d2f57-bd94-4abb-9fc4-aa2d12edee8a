"use client";
import React, { useState } from "react";
import {
  <PERSON>ton,
  Box,
  Typography,
  FormControl,
  FormHelperText,
  IconButton,
  InputAdornment,
  InputLabel,
  CircularProgress,
  OutlinedInput,
  Alert ,
  TextField,
} from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { signIn } from "next-auth/react";

const LoginPage = () => {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [userDetails, setUserDetails] = useState({
    email: "",
    password: "",
  });
  const [invalid, setInvalid] = useState("");
  const [loginError, setLoginError] = useState({
    emailError: "",
    passwordError: "",
  });
  const [pending, setPending] = useState(false);
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

  const passRegex =
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@.#$!%*?&])[A-Za-z\d@.#$!%*?&]{8,15}$/;

  const handleOnChange = (e) => {
    const { name, value } = e.target;
    setUserDetails((prevState) => ({
      ...prevState,
      [name]: value,
    }));

    if (name === "email") {
      if (!value.trim()) {
        setLoginError((prevError) => ({
          ...prevError,
          emailError: "Email is required*",
        }));
      } else if (!emailRegex.test(value)) {
        setLoginError((prevError) => ({
          ...prevError,
          emailError: "Invalid email format",
        }));
      } else {
        setLoginError((prevError) => ({
          ...prevError,
          emailError: "",
        }));
      }
    }

    if (name === "password") {
      if (!value.trim()) {
        setLoginError((prevError) => ({
          ...prevError,
          passwordError: "Password is required*",
        }));
      } else if (!passRegex.test(value)) {
        setLoginError((prevError) => ({
          ...prevError,
          passwordError:
            "Password must be 8-15 characters long and contain at least one lowercase letter, one uppercase letter, one number, and one special character",
        }));
      } else {
        setLoginError((prevError) => ({
          ...prevError,
          passwordError: "",
        }));
      }
    }
    setInvalid("");
  };
  const handleFormSubmit = async (e) => {
    e.preventDefault();
    setLoginError({
      emailError: "",
      passwordError: "",
    });
    if (!userDetails.email.trim()) {
      setLoginError((prevState) => ({
        ...prevState,
        emailError: "Please enter your email",
      }));
      return;
    }
    if (!userDetails.password.trim()) {
      setLoginError((prevState) => ({
        ...prevState,
        passwordError: "Please enter your password",
      }));
      return;
    }
    try {
      setPending(true);
      const response = await signIn("credentials", {
        email: userDetails.email,
        password: userDetails.password,
        redirect: false,
      });
      if (response.status === 401) {
        setInvalid("Invalid Email or Password !!");
        setPending(false);
        return;
      }
      router.replace("/");

    } catch (error) {
      setPending(false);
      console.log("Error:", error);
      setInvalid("Invalid Email or Password");
    }
  };

  const handleClickShowPassword = () => setShowPassword((show) => !show);

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  return (
    <Box
      sx={{
        display: "flex",
        minHeight: "100vh",
        backgroundColor: "#f8fafc"
      }}
    >
      <Box
        sx={{
          display: { xs: "none", md: "flex" },
          width: "50%",
          backgroundColor: "#00A3FF",
          justifyContent: "center",
          alignItems: "center",
          padding: "2rem"
        }}
      >
        <Box sx={{ maxWidth: "500px", textAlign: "center" }}>
          <Typography
            variant="h3"
            sx={{
              fontWeight: 800,
              color: "white",
              mb: 3,
              fontSize: "2rem"
            }}
          >
             Welcome Back to AppsStoreSpy
          </Typography>
          
          <Typography
            variant="body1"
            sx={{
              color: "rgba(255,255,255,0.9)",
              fontSize: "1.1rem",
              lineHeight: 1.6
            }}
          >
            Track, analyze and optimize your play store performance with our powerful analytics platform.
          </Typography>
          <Box sx={{ mt: 4 }}>
            <img
              src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
              alt="App Analytics"
              style={{ width: "100%", borderRadius: "8px" }}
            />
          </Box>

        </Box>
      </Box>

      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          width: { xs: "100%", md: "50%" },
          padding: { xs: "2rem", md: "4rem" }
        }}
      >
        <Box sx={{ mb: 4, textAlign: "center" }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              color: "#101828",
              mb: 1
            }}
          >
            Login to your account
          </Typography>
          <Typography variant="body1" sx={{ color: "#667085" }}>
            Welcome back! Please enter your details
          </Typography>
        </Box>

        <Box
          sx={{
            width: "100%",
            maxWidth: "500px",
            margin: "0 auto",
            backgroundColor: "white",
            borderRadius: "12px",
            boxShadow: "0 4px 12px rgba(0, 0, 0, 0.08)",
            padding: { xs: "1.5rem", sm: "2rem" }
          }}
        >
          {invalid && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {invalid}
            </Alert>
          )}

          <form onSubmit={handleFormSubmit}>
            <Box sx={{ mb: 2 }}>
              <TextField
                fullWidth
                name="email"
                label="Email"
                variant="outlined"
                onChange={handleOnChange}
                error={Boolean(loginError.emailError)}
                helperText={loginError.emailError}
              />
            </Box>

            <Box sx={{ mb: 1 }}>
              <FormControl fullWidth variant="outlined">
                <InputLabel htmlFor="password">Password</InputLabel>
                <OutlinedInput
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  onChange={handleOnChange}
                  error={Boolean(loginError.passwordError)}
                  endAdornment={
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={handleClickShowPassword}
                        onMouseDown={handleMouseDownPassword}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  }
                  label="Password"
                />
                {loginError.passwordError && (
                  <FormHelperText error>
                    {loginError.passwordError}
                  </FormHelperText>
                )}
              </FormControl>
            </Box>

            <Box sx={{
              display: "flex",
              justifyContent: "flex-end",
              mb: 3
            }}>
              <Link
                href="/forget-password"
                sx={{
                  color: "#00A3FF",
                  fontWeight: 500,
                  fontSize: "0.875rem",
                  textDecoration: "none",
                  "&:hover": { textDecoration: "underline" }
                }}
              >
                Forgot password?
              </Link>
            </Box>

            <Button
              fullWidth
              variant="contained"
              size="large"
              type="submit"
              disabled={pending}
              sx={{
                backgroundColor: "#00A3FF",
                py: 1.5,
                borderRadius: "8px",
                textTransform: "none",
                fontSize: "1rem",
                fontWeight: 600,
                "&:hover": {
                  backgroundColor: "#0088cc"
                }
              }}
            >
              {pending ? (
                <>
                  <CircularProgress size={24} sx={{ color: "white", mr: 1 }} />
                  Logging in...
                </>
              ) : (
                "Login Now"
              )}
            </Button>
          </form>

          <Box sx={{ mt: 3, textAlign: "center" }}>
            <Typography variant="body2" sx={{ color: "#667085" }}>
              Don't have an account?{" "}
              <Link
                href="/register"
                sx={{
                  color: "#00A3FF",
                  fontWeight: 600,
                  textDecoration: "none",
                  "&:hover": { textDecoration: "underline" }
                }}
              >
                Sign Up
              </Link>
            </Typography>
          </Box>
        </Box>

        <Box sx={{ mt: 4, textAlign: "center" }}>
          <Typography variant="body2" sx={{ color: "#98A2B3" }}>
            © {new Date().getFullYear()} AppsStoreSpy. All rights reserved.
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default LoginPage;
